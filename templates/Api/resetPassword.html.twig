{% extends 'Frame/Layout/base.html.twig' %}

{% block title %} {{ "Portal"|trans({}, 'across') }} | ABUS Kransysteme GmbH{% endblock %}

{% block bodyClass %}layoutNotAuthenticated login{% endblock %}

{% block stylesheets %}
    <style>
        [v-cloak] {
            display: none !important;
        }
    </style>

    <link rel="stylesheet" href="{{ asset('dist/public/abus/app/app.css') }}" />
    <link rel="stylesheet" href="{{ asset('dist/public/abus/resetPassword/resetPassword.css') }}" />
{% endblock %}

{% block javascripts %}
    <script src="{{ asset('dist/public/abus/app/app.js') }}"></script>
    <script src="{{ asset('dist/public/abus/resetPassword/resetPassword.js') }}"></script>
{% endblock %}

{% block body %}

    <div class="container-fluid">

        <div class="outer">

            <img src="{{ asset('build/images/abus/logos/logo_without_subline.png') }}" class="logo" alt="ABUS Kransysteme GmbH">

            {% include 'Frame/Layout/Parts/LangFlagsBar.html.twig' %}

            {% block content %}
                <div id="ResetPwd" v-cloak :init="resetPasswordRoute = '{{ path('abus_api_reset_password_post', {'subdomain': getSubdomain('portal'), 'domain': getDomain(), 'hash': hash}) }}'">

                    <div class="container message-container" v-show="!resettedPwd && backendMessage !== ''">
                        <div class="alert alert-danger" v-html="backendMessage"></div>
                    </div>

                    <div class="container message-container" v-show="resettedPwd">
                        <div class="alert"  :class="{'alert-success': resettedPwd, 'alert-danger': !resettedPwd}" v-html="backendMessage"></div>
                    </div>

                    <div class="card mb-3">
                        <div class="card-header bg-secondary">{{ "api/password/password_policy"|trans({}, 'across') }}</div>
                        <div class="card-body bg-white text-left">
                            <div class="container-fluid">

                                <div class="row alert alert-info">
                                    <div class="col-1"><i class="fa fa-info-circle alert-icon d-none d-md-block"></i></div>
                                    <div class="col-11">
                                        {{ "api/password/password_name_1"|trans({}, 'across') }}<br /><br>
                                    </div>
                                </div>

                                <div class="row alert" :class="{'alert-success' : length(), 'alert-danger' : !length()}">
                                    <div v-show="!length()" class="col-1"><i class="fa fa-exclamation-triangle alert-icon d-none d-md-block"></i></div>
                                    <div v-show="length()" class="col-1"><i class="fa fa-check alert-icon d-none d-md-block"></i></div>
                                    <div class="col-11">{{ "api/password/password_complexity_length"|trans({}, 'across') }}</div>
                                </div>

                                <h5 class="mt30" style="color: #000000;">{{ "api/password/password_threeOfFour"|trans({}, 'across') }}</h5>

                                <div class="row alert" :class="{'alert-success' : uppercase(), 'alert-warning' : !uppercase()}">
                                    <div v-show="!uppercase()" class="col-1"><i class="fa fa-exclamation-triangle alert-icon d-none d-md-block"></i></div>
                                    <div v-show="uppercase()" class="col-1"><i class="fa fa-check alert-icon d-none d-md-block"></i></div>
                                    <div class="col-11">{{ "api/password/password_complexity_alpha_uppercase"|trans({}, 'across') }}</div>
                                </div>

                                <div class="row alert" :class="{'alert-success' : lowercase(), 'alert-warning' : !lowercase()}">
                                    <div v-show="!lowercase()" class="col-1"><i class="fa fa-exclamation-triangle alert-icon d-none d-md-block"></i></div>
                                    <div v-show="lowercase()" class="col-1"><i class="fa fa-check alert-icon d-none d-md-block"></i></div>
                                    <div class="col-11">{{ "api/password/password_complexity_alpha_lowercase"|trans({}, 'across') }}</div>
                                </div>

                                <div class="row alert" :class="{'alert-success' : digits(), 'alert-warning' : !digits()}">
                                    <div v-show="!digits()" class="col-1"><i class="fa fa-exclamation-triangle alert-icon d-none d-md-block"></i></div>
                                    <div v-show="digits()" class="col-1"><i class="fa fa-check alert-icon d-none d-md-block"></i></div>
                                    <div class="col-11">{{ "api/password/password_complexity_alpha_number"|trans({}, 'across') }}</div>
                                </div>

                                <div class="row alert" :class="{'alert-success' : specialChar(), 'alert-warning' : !specialChar()}">
                                    <div v-show="!specialChar()" class="col-1"><i class="fa fa-exclamation-triangle alert-icon d-none d-md-block"></i></div>
                                    <div v-show="specialChar()" class="col-1"><i class="fa fa-check alert-icon d-none d-md-block"></i></div>
                                    <div class="col-11">{{ "api/password/password_complexity_special"|trans({}, 'across') }}</div>
                                </div>

                            </div>

                        </div>
                    </div>

                    <form @submit.prevent="resetPassword()" v-show="!resettedPwd">
                        <div class="form-group">
                            <input type="password" name="password1" @input="validatePWComplexity()" v-model="password1" class="form-control" placeholder="{{ "api/password/set_new_password"|trans({}, 'across') }}" >
                        </div>
                        <div class="form-group">
                            <input type="password" name="password2" v-model="password2" class="form-control" placeholder="{{ "api/password/repeat_new_password"|trans({}, 'across') }}">
                        </div>
                        <button type="submit" class="btn btn-success" :disabled="password1 == '' || password1 != password2 || !validatePWComplexity()">{{ "api/password/reset"|trans({}, 'across') }}</button>
                    </form>

                </div>

            {% endblock %}

        </div>

    </div>

    <div class="footer">

        <div id="help" class="container">
            <span>{{ "frame/any_problems"|trans({}, 'across') }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a class="ui-link noSpinner" href="mailto: <EMAIL>?subject=ABUS Portal"><EMAIL></a></span>
        </div>

    </div>
{% endblock %}