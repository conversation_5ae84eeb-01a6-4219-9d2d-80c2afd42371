{% extends 'Frame/Layout/base.html.twig' %}

{% block bodyClass %}layoutNotAuthenticated login{% endblock %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('dist/public/abus/app/app.css') }}" />
    <link rel="stylesheet" href="{{ asset('dist/public/abus/register/register.css') }}" />
{% endblock %}

{% block body %}
<div data-role="page" id="grantpermission" class="default-background text-dark">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100vh; background-color: #0058A1; padding: -20px;">

        <div style="width: 980px; margin: 20px auto 0; padding: 20px; background-color: #e8e8e8;">

            <div class="my-4">
                {% if account.type == 'create' %}

                    <h2>{{ "account/abukonfis/subtitle/allowed"|trans({}, 'across') }}</h2>

                    <table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0; width: 100%;">
                        <tr><td style="width: 150px;">{{ "account/workflow/User"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.newAccountHolder }}</td></tr>
                        <tr><td>{{ "newregister/email"|trans({}, 'across') }}</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.accountHolderEmail }}</td></tr>
                        <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.samaccountname }}</td></tr>
                        <tr><td>{{ "account/workflow/Company"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.company }}</td></tr>
                        {% if account.serialNumber is not empty %}<tr><td>{{ "Token serial number"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.serialNumber }}</td></tr>{% endif %}
                        <tr><td colspan="3"><hr></td></tr>
                        {% if account.remarkField is not empty %}<tr><td>{{ "account/Remark"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.remarkField|raw }}</td></tr>{% endif %}
                        <tr><td colspan="3"><hr></td></tr>
                        <tr><td>{{ "account/Reference/user"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.referenceAccountHolder }}</td></tr>
                        <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.samaccountname2 }}</td></tr>
                        {% if account.token == 1 %}<tr><td>{{ "Token"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ "Please send him a new token"|trans({}, 'across') }}</td></tr>{% endif %}
                    </table>

                    <br />

                {% elseif account.type == 'delete' %}

                    <h2>{{ "abus/account/delete"|trans({}, 'across') }}</h2>

                    <table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0; width: 100%;">
                        <tr><td style="width: 150px;">{{ "account/workflow/User"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.oldAccountHolder }}</td></tr>
                        <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.samaccountname }}</td></tr>
                        <tr><td>{{ "account/workflow/Company"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.company }}</td></tr>
                        {% if account.keepToken == 1 %}<tr><td>{{ "Token"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ "We'll keep the token for later usage"|trans({}, 'across') }}</td></tr>{% endif %}
                        {% if account.ovissReceiver != "" %}
                            <tr><td colspan="3"><hr></td></tr>
                            <tr><td>{{ "account/oviss/receiver"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.ovissReceiver }}</td></tr>
                            <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.samaccountname2 }}</td></tr>
                        {% endif %}
                    </table>

                    <br />

                {% elseif account.type == 'vpn_create' %}

                    <h2>{{ "account/vpn/create/subtitle/allowed"|trans({}, 'across') }}</h2>

                    <table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0; width: 100%;">
                        <tr><td style="width: 150px;">{{ "account/workflow/User"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.newAccountHolder }}</td></tr>
                        <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.samaccountname }}</td></tr>
                        <tr><td>{{ "account/workflow/Company"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.company }}</td></tr>
                        <tr><td>{{ "account/Remark"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.remarkField }}</td></tr>
                    </table>

                    <br />

                {% elseif account.type == 'vpn_reassign' %}

                    <h2>{{ "account/vpn/reassign"|trans({}, 'across') }}</h2>

                    <table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0; width: 100%;">
                        <tr><td style="width: 150px;">{{ "account/workflow/Company"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.company }}</td></tr>
                        <tr><td>{{ "account/workflow/User"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.newAccountHolder }}</td></tr>
                        <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.samaccountname }}</td></tr>
                        <tr><td>{{ "account/Remark"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ account.remarkField }}</td></tr>
                        <tr><td colspan="3"><hr></td></tr>
                    </table>

                    <br />

                {% endif %}

                {% if (account.messagestep1 is defined and account.messagestep1 != '') or (account.messagestep2 is defined and account.messagestep2 != '') or (account.messagestep3 is defined and account.messagestep3 != '') or (account.messagestep4 is defined and account.messagestep4 != '') %}

                    <table style="width: 100%; font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0;">

                        <tr><td colspan="2" style="font-weight: bold; font-size: 1.2em; border-bottom: #888888 1px solid;">{{ "abus/account/messages"|trans({}, 'across') }}</td></tr>
                        <tr><td colspan="2" style=" font-size: 0.6em;">&nbsp;</td></tr>

                        {% if account.messagestep1 is defined and account.messagestep1 != '' %}
                            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ account.type == 'vpn_create' or account.type == 'vpn_reassign' ? 'Vertrieb-Organisation' : "Marketing"|trans({}, 'across') }}:</td><td width="90%">{{ account.messagestep1|nl2br }}</td></tr>
                        {% endif %}
                        {% if account.messagestep2 is defined and account.messagestep2 != '' %}
                            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ "Entwicklung-Organisation"|trans({}, 'across') }}:</td><td>{{ account.messagestep2|nl2br }}</td></tr>
                        {% endif %}
                        {% if account.messagestep3 is defined and account.messagestep3 != '' %}
                            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ "IT"|trans({}, 'across') }}:</td><td>{{ account.messagestep3|nl2br }}</td></tr>
                        {% endif %}
                        {% if account.messagestep4 is defined and account.messagestep4 != '' %}
                            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ "ProALPHA"|trans({}, 'across') }}:</td><td>{{ account.messagestep4|nl2br }}</td></tr>
                        {% endif %}
                        {% if account.messagestep5 is defined and account.messagestep5 != '' %}
                            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ "Vertrieb-Organisation"|trans({}, 'across') }}:</td><td>{{ account.messagestep5|nl2br }}</td></tr>
                        {% endif %}
                        {% if account.messagestep6 is defined and account.messagestep6 != '' %}
                            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ "OVISS"|trans({}, 'across') }}:</td><td>{{ account.messagestep6|nl2br }}</td></tr>
                        {% endif %}
                    </table>
                {% endif %}
            </div>

            <form action="" enctype="application/x-www-form-urlencoded" method="post">

                <label for="message" class="form-label mb-2">{{ "abus/account/message"|trans({}, 'across') }}:</label>
                <textarea name="message" class="form-control" rows="3" placeholder="{{ 'abus/account/message/placeholder'|trans({}, 'across') }}"></textarea>

                <input type="submit" class="form-control btn btn-success mt-4 text-center bg-success text-white" value="{{ buttonText|trans({}, 'across') }}">
            </form>

            <div style="clear: both;"></div>

        </div>
    </div>

    {% endblock %}

