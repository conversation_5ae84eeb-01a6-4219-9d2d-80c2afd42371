{% extends 'Frame/Layout/layout.html.twig' %}

{% block title %} {{ "module/usermanagement_user_management"|trans({}, 'across') }} | ABUS Kransysteme GmbH{% endblock %}

{% block javascripts %}
    {{ parent() }}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
{% endblock %}

{% block headline %}{{ "module/usermanagement_user_management"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}



{% block administration %}
    {{ parent() }}

    {% if is_granted('ROLE_PORTAL_NEWS_ADMIN') %}
        <div class="sidebar-panel">
            <h5 class="sidebar-panel-title">
                {{ ["usermanagement/administration"]|join|trans({}, 'across') }}
            </h5>
        </div>

        <div class="content" >
            <create-company-modal-button></create-company-modal-button>
        </div>
    {% endif %}
    {#<div class="sidebar-panel">#}
        {#<h5 class="sidebar-panel-title">{{ "Administration"|trans({}, 'frame') }}</h5>#}
    {#</div>#}

    {#<div class="content" ng-controller="tdataSelectController">#}
        {#{{ "Select database"|trans({}, 'tdata') }}#}
        {#<select class="form-control" name="preview" ng-model="preview" {% if preview %}ng-init="preview = '1';"{% endif %} ng-change="database()">#}
            {#<option value="0" selected="selected">{{ "Live database"|trans({}, 'tdata') }}</option>#}
            {#<option value="1">{{ "Preview database"|trans({}, 'tdata') }}</option>#}
        {#</select>#}

        {#<br>#}
        {#<button type="button" class="btn btn-primary btn-block mr5" data-toggle="modal" data-target="#indexModal" ng-click="">{{ "Index database"|trans({}, 'tdata') }}</button>#}
        {#<a href="{{ path('abus_tdata_crawler', {'subdomain': getSubdomain('tdata'), 'domain': getDomain()}) }}" type="button" class="btn btn-primary btn-block mr5 mb10">{{ "Crawl database"|trans({}, 'tdata') }}</a>#}
    {#</div>#}
{% endblock %}


{% block content %}

    <user-management></user-management>

{% endblock %}

{% block custom_javascripts %}{% endblock %}

{% block footer %}{% endblock %}
