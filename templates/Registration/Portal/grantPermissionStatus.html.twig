{% extends 'Frame/Layout/base.html.twig' %}

{% block bodyClass %}layoutNotAuthenticated login{% endblock %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('dist/public/abus/app/app.css') }}" />
    <link rel="stylesheet" href="{{ asset('dist/public/abus/register/register.css') }}" />
{% endblock %}

{% block body %}
<div class="text-dark" style="position: absolute; top: 0; left: 0; width: 100%; height: 100vh; background-color: #0058A1; padding: -20px;">

    <div style="width: 980px; margin: 20px auto 0; padding: 20px; background-color: #e8e8e8;">

        <h2>ABUS Portal</h2>

        {% for service in serviceStatus %}
        {% set level = '' %}

            {% if service.status == null %}
                <div class="alert font-weight-bold alert-secondary" role="alert">
            {% elseif service.status == 'false' %}
                <div class="alert font-weight-bold alert-danger" role="alert">
                {% set level = [' - ', 'false']|join %}
            {% else %}
                <div class="alert font-weight-bold alert-success" role="alert">
                {% set level = [' - ', 'true']|join %}
            {% endif %}

            {{ service.name|raw|trans({}, 'across') }}

            {% if level == ' - true' %}
                {{ [' - ', 'service/approved'|raw|trans({}, 'across')]|join }}
            {% elseif level == ' - false' %}
                {{ [' - ', 'service/rejected'|raw|trans({}, 'across')]|join }}
            {% else %}
                {{ [' - ', 'service/not yet approved'|raw|trans({}, 'across')]|join }}
            {% endif %}
            </div>
        {% endfor %}
    </div>
</div>

{% endblock %}
