{% extends 'Frame/Layout/base.html.twig' %}

{% block bodyClass %}layoutNotAuthenticated login{% endblock %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('dist/public/abus/app/app.css') }}" />
    <link rel="stylesheet" href="{{ asset('dist/public/abus/register/register.css') }}" />
{% endblock %}

{% block body %}
<div data-role="page" id="grantpermission" class="default-background text-dark">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100vh; background-color: #0058A1; padding: -20px;">

        <div style="width: 980px; margin: 20px auto 0; padding: 20px; background-color: #e8e8e8;">

            <h2>ABUS Portal</h2>

            <div class="my-2"> Welcher Zugang soll für <b>{{ username }}</b> in <b>{{ service_name|raw|trans({}, 'across') }}</b> freigegeben werden?</div>

            <form action="" method="post" data-ajax="false">

                <div class="form-group w-50">
                    <input type="text" class="form-control is-invalid" name="customernumber" maxlength="20" onChange="activateButton()" placeholder="Kundennummer" required id="customernumber">
                    <div class="ml-1 invalid-feedback">
                        Dieses Feld ist pflicht!
                    </div>
                </div>

                <div class="form-group w-50">
                    <select class="form-control mb-3 is-invalid" name="level" onChange="activateButton()" aria-label="Default select example" id="level">
                        {% for key, item in options %}
                            <option value="{{ key }}">{{ item }}</option>
                        {% endfor %}
                    </select>
                    <div class="ml-1 invalid-feedback">
                        Mit #Sonstige kann der Dienst nicht freigegeben werden, wählen Sie was anderes aus bitte!
                    </div>
                </div>

                <input type="submit" disabled="disabled" class="btn btn-success small" name="allow" id="next" onclick="return validate()" value="{{ "Allow access"|trans({}, 'across') }}" data-theme="d" data-icon="arrow-r" data-iconpos="right" />

            </form>
        </div>

    </div>

    {% endblock %}

    {% block javascripts %}

        {{ parent() }}

        <script>
            const customernumber = document.querySelector('#customernumber');
            const level = document.querySelector('#level');
            const btn = document.querySelector('#next');

            function activateButton() {
                if (customernumber.value.length > 0 && level.value != '<EMAIL>') {
                    customernumber.classList.remove('is-invalid');
                    level.classList.remove('is-invalid');
                    btn.removeAttribute('disabled');
                    return;
                }
                btn.setAttribute('disabled', 'disabled');
                customernumber.classList.add('is-invalid');
                level.classList.add('is-invalid');
            }
        </script>
    {% endblock %}
