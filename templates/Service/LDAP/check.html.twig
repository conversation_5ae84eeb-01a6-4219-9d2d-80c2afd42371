{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "service/activedirectory/check/headline"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    <div class="container-fluid mt-3 p-0">
        <div class="row">
            {% if collisionDetected %}

                <div class="col-md-8 mb-2">
                    <div class="p-2" style="background-color: red; color: #ffffff;">{{ "service/There are potential collisions with existing users"|trans({}, 'across') }}</div>

                    <table class="table" style="font-size: 14px;">
                        <thead>
                            <tr>
                                <th>displayname</th>
                                <th>company</th>
                                <th>samaccountname</th>
                                <th>dn</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for collision in collisionDetected %}
                                <tr>
                                    <td>{{ collision.displayname }}</td>
                                    <td>{{ collision.company }}</td>
                                    <td>{{ collision.samaccountname }}</td>
                                    <td>{{ collision.dn }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>

                    </table>
                </div>
            {% endif %}
        </div>

        <form method="post" enctype="application/x-www-form-urlencoded" action="" data-ajax="false">
            <div class="row">
                <div class="col-md-8">

                    <input type="hidden" class="form-control" name="create" value="{{ registration.id }}">

                    <div class="alert alert-danger" id="language-error">
                        Sie müssen die Sprache auswählen, um fortzufahren!
                    </div>
                    <div class="row mb-4 p-3 m-0" style="background-color: #318a3f; color: #ffffff;">
                        <div class="col-md-7 p-0" colspan="3" style="font-size: 20px;">{{"service/language for mail"|trans({}, 'across')}}</div>
                            <select name="sendmailLanguage" id="sendmailLanguage" onChange="validateLanguage()" class="offset-md-1 col-md-4 form-control">
                                <option value=""></option>
                                <option value="de_DE">{{ "service/german"|trans({}, 'across') }}</option>
                                <option value="en_GB">{{ "service/english"|trans({}, 'across') }}</option>
                            </select>
                    </div>


                    <div class="row mb-3">
                        <div class="col-md-2 center">{{ "newregister/name"|trans({}, 'across') }}</div>
                        <div class="col-md-2">
                            <select name="gender" class="form-control error" placeholder="Anrede">
                                <option value="0" {% if registration.gender == 0 %}selected{% endif %}>{{ "newregister/mr"|trans({}, 'across') }}</option>
                                <option value="1" {% if registration.gender == 1 %}selected{% endif %}>{{ "newregister/mrs"|trans({}, 'across') }}</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="text" name="title" class="form-control" value="{{ registration.title }}" placeholder="{{ "newregister/title"|trans({}, 'across') }}">
                        </div>
                        <div class="col-md-3">
                            <input type="text" id="firstname" name="firstname" class="form-control" value="{{ registration.firstname }}" placeholder="{{ "newregister/firstname"|trans({}, 'across') }}">
                        </div>
                        <div class="col-md-3">
                            <input type="text" id="lastname" name="lastname" class="form-control" value="{{ registration.lastname }}" placeholder="{{ "newregister/lastname"|trans({}, 'across') }}">
                        </div>
                    </div>

                    <company-suggestion :companies="{{ companySuggestions | json_encode }}" :registration="{{ registration | json_encode }}"></company-suggestion>

                    {% if registration.customernumber != '' %}
                        <div class="row mb-3">
                            <div class="col-md-2">{{ "newregister/customernumber"|trans({}, 'across') }}</div>
                            <div class="col-md-4">
                                <input type="text" name="customernumber" class="form-control" value="{{ registration.customernumber }}" placeholder="{{ "newregister/customernumber"|trans({}, 'across') }}">
                            </div>
                        </div>
                    {% endif %}

                    <div class="row mb-3">
                        <div class="col-md-2">{{ "newregister/position"|trans({}, 'across') }}</div>
                        <div class="col-md-4">
                            <input type="text" name="position" class="form-control" value="{{ registration.function }}" readonly>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-2">{{ "newregister/street"|trans({}, 'across') }}</div>
                        <div class="col-md-4">
                            <input type="text" name="street" class="form-control" value="{{ registration.street }}" placeholder="{{ "newregister/street"|trans({}, 'across') }}">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-2">{{ "newregister/location"|trans({}, 'across') }}</div>
                        <div class="col-md-2">
                            <input type="text" name="postcode" class="form-control" value="{{ registration.postcode }}" placeholder="{{ "newregister/postcode"|trans({}, 'across') }}">
                        </div>
                        <div class="col-md-2">
                            <input type="text" name="town" class="form-control" value="{{ registration.town }}" placeholder="{{ "newregister/town"|trans({}, 'across') }}">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-2">{{ "newregister/country"|trans({}, 'across') }}</div>
                        <div class="col-md-4">
                            <input type="text" name="country" class="form-control" value="{{ registration.country }}" placeholder="{{ "newregister/country"|trans({}, 'across') }}">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-2">{{ "newregister/telephone"|trans({}, 'across') }}</div>
                        <div class="col-md-4">
                            <input type="text" name="telephone" class="form-control" value="{{ registration.telephone }}" placeholder="{{ "newregister/telephone"|trans({}, 'across') }}">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-2">{{ "newregister/email"|trans({}, 'across') }}</div>
                        <div class="col-md-4">
                            <input type="text" name="email" class="form-control" value="{{ registration.email }}" placeholder="{{ "newregister/email"|trans({}, 'across') }}">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-2">{{ "newregister/adUsername"|trans({}, 'across') }}</div>
                        <div class="col-md-4">
{#                            <input type="text" id="username" name="username" class="form-control" value="{{ username }}" placeholder="AD-Benutzername">#}
                            <is-valid-username name="{{ username }}" path="{{ path('abus_portal_validate_username') }}"></is-valid-username>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-2">{{ "newregister/password"|trans({}, 'across') }}</div>
                        <div class="col-md-4">
                            <input type="text" name="password" class="form-control" value="{{ password }}" placeholder="{{ "newregister/password"|trans({}, 'across') }}">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-2">
                <div class="col-md-8">

                    <h4>{{ "service/usergroup"|trans({}, 'across') }}</h4>

                    <div class="alert alert-danger" id="group-error">
                        Sie müssen eine Gruppe auswählen, um fortzufahren!
                    </div>

                    <select name="groups[]" class="form-control is-invalid" id="group" onChange="validateGroup()">
                        <option></option>
                        <option value="ABUS Intern">ABUS intern</option>
                        <option value="PORTAL_GRUPPE_TOECHTER">Töchter</option>
                        <option value="PORTAL_GRUPPE_EXPORTPARTNER">Vertriebspartner Export</option>
                        <option value="PORTAL_GRUPPE_VERTRIEBSPARTNER_INLAND">Vertriebspartner Inland </option>
                        <option value="PORTAL_GRUPPE_WIEDERVERKAEUFER_HAENDLER">Wiederverkäufer / Händler</option>
                        <option value="PORTAL_GRUPPE_ARCHITEKTEN_PLANER_STATIKER">Architekten, Planer, Statiker etc.</option>
                        <option value="PORTAL_GRUPPE_PARTNER_DER_EXPORTPARTNER">Partner der Exportpartner</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="custom-control custom-checkbox w-100 border my-2 py-2">
                        <input type="checkbox" id="Abnahmepartner" name="groups[]" value="PORTAL_GRUPPE_ABNAHMEPARTNER" class="custom-control-input px-2">
                        <label for="Abnahmepartner" class="custom-control-label font-weight-bold">Abnahmepartner</label>
                    </div>
                    <div class="custom-control custom-checkbox w-100 border my-2 py-2">
                        <input type="checkbox" id="Montagepartner" name="groups[]" value="PORTAL_GRUPPE_MONTAGEPARTNER" class="custom-control-input px-2">
                        <label for="Montagepartner" class="custom-control-label font-weight-bold">Montagepartner</label>
                    </div>
                    <div class="custom-control custom-checkbox w-100 border my-2 py-2">
                        <input type="checkbox" id="ServicepartnerBasic" name="groups[]" value="PORTAL_GRUPPE_SERVICEPARTNER_BASIC" class="custom-control-input px-2">
                        <label for="ServicepartnerBasic" class="custom-control-label font-weight-bold">Servicepartner Basic</label>
                    </div>
                    <div class="custom-control custom-checkbox w-100 border my-2 py-2">
                        <input type="checkbox" id="Servicepartner" name="groups[]" value="PORTAL_GRUPPE_SERVICEPARTNER" class="custom-control-input px-2">
                        <label for="Servicepartner" class="custom-control-label font-weight-bold">Servicepartner</label>
                    </div>
                    <div class="custom-control custom-checkbox w-100 border my-2 py-2">
                        <input type="checkbox" id="ServicepartnerMaster" name="groups[]" value="PORTAL_GRUPPE_SERVICEPARTNER_MASTER" class="custom-control-input px-2">
                        <label for="ServicepartnerMaster" class="custom-control-label font-weight-bold">Servicepartner Master</label>
                    </div>
                </div>
            </div>

            <div class="row mt-2 mb-3">
                <div class="col-md-8">
                    <h4>{{ "service/otherGroups"|trans({}, 'across') }}</h4>
                </div>
                <div class="col-md-8">
                    <div class="custom-control custom-checkbox w-100 border my-2 py-2">
                        <input type="checkbox" id="ABUTools" name="groups[]" value="PORTAL_ABUTOOLS_TECHNIK_USERMANAGEMENT_ENABLED" class="custom-control-input px-2">
                        <label for="ABUTools" class="custom-control-label font-weight-bold">ABUTools (Technik)</label>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <table class="table">

                    <tr>
                        <th>{{ "service/requestedService"|trans({}, 'across') }}</th>
                        <th>{{ "service/approved"|trans({}, 'across') }}</th>
                        <th>{{ "service/adGroup"|trans({}, 'across') }}</th>
                    </tr>

                    <tr>
                        <td>ABUS PORTAL</td>
                        <td></td>
                        <td>
                            <select name="groups[]" class="form-control">
                                <option value="PORTAL_2018">{{ "YES"|trans({}, 'across') }}</option>
                                <option value="">{{ "NO"|trans({}, 'across') }}</option>
                            </select>
                        </td>
                    </tr>

                    {% set tr = false %}

                    {% for service in services %}

                        {#{% if service.name != 'ABUS Teamroom' %}#}
                        <tr style="{% if service.permissionGranted == false %}color: red;{% else %}color: green;{% endif %}">
                            <td>
                                {{ service.name |trans({}, 'across') }}
                                {% if service.name == 'ABUS Teamroom' %}
                                    - {{ service.subModules.0.name|replace({'<span style="font-size: 0.8em;"></span>': ''}) }}

                                    {% if service.permissionGranted %}<input type="hidden" name="teamrooms[]" value="{{ service.subModules.0.id }}">{% endif %}

                                {% endif %}
                            </td>
                            <td>
                                {% if service.permissionGranted == false %}
                                    {{ "service/rejected"|trans({}, 'across') }}
                                {% else %}

                                    {% if service.permissionGranted is iterable %}
                                        {% for permission in service.permissionGranted %}
                                            <div>{{ permission }}</div>
                                        {% endfor %}
                                    {% else %}
                                        {% if service.permissionGranted == '1' %}
                                            {{ "service/approved"|trans({}, 'across') }}
                                        {% else %}
                                            {{ service.permissionGranted }}
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                            </td>
                            <td class="select">
                                {% if service.permissionGranted != false and tr == false and service.uniqueIdentifier not in ['41465927675b6c53285e383020', '5f66684a4b4e3c7a5067274a67', '5e666d64295943792c5f4c6248'] %}
                                    <select name="groups[]" class="form-control">
                                        <option></option>
                                        {% for ldapGroup in service.ldapGroups %}
                                            {% if type == 'registration' and service.uniqueIdentifier == '5e666d64295943792c5f4c6248' %}
                                            {% else %}
                                                <option {% if service.ldapGroups|length == 1 %} selected {% endif %} value="{{ ldapGroup.name }}">{{ ldapGroup.name |trans({}, 'across') }}</option>
                                            {% endif %}
                                        {% endfor %}

                                        {% if service.permissionGranted is not iterable and '@' in service.permissionGranted and type == 'registration' and service.uniqueIdentifier == '5e666d64295943792c5f4c6248' %}
                                            <option value="PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_LK_SK_LPK_HB">PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_LK_SK_LPK_HB</option>
                                        {% endif %}
                                    </select>
                                {% endif %}
                                {% if tr %}
                                    <span style="color: #000000; font-size: 12px; font-style: italic;">wird mit gleicher AD Gruppe wie erster Teamroom angelegt</span>
                                {% endif %}
                            </td>
                        </tr>
                        {#{% endif %}#}

                        {# Teamroom nur einmal listen #}
                        {% if service.name == 'ABUS Teamroom' and service.permissionGranted != false %}
                            {% set tr = true %}
                        {% endif %}

                    {% endfor %}

                </table>
                </div>
            </div>

            <input type="hidden" name="requestedServices" class="form-control" value="{{ requestedServices }}">

            <div class="row my-3">
                <div class="col-md-8">
                    <button type="submit" id="create" class="btn btn-primary w-100" disabled="disabled">{{ "service/createUser"|trans({}, 'across') }}</button>
                </div>
            </div>
        </form>
    </div>

{% endblock content %}
{% block javascripts %}

    {{ parent() }}

    <script>

        const btn = document.querySelector('#create');

        function validateLanguage() {

            const sendmailLanguage = document.querySelector('#sendmailLanguage');
            const languageerror = document.querySelector('#language-error');

            if(sendmailLanguage.value.length > 0) {

                if(group.value.length > 0) {
                    btn.removeAttribute('disabled');
                }

                languageerror.classList.add('d-none');
                sendmailLanguage.classList.remove('is-invalid')
                return;
            }
            btn.setAttribute('disabled', 'disabled');
            languageerror.classList.remove('d-none');
            sendmailLanguage.classList.add('is-invalid');
        }

        function validateGroup() {

            const group = document.querySelector('#group');
            const grouperror = document.querySelector('#group-error');

            if(group.value.length > 0) {

                if(sendmailLanguage.value.length > 0) {
                    btn.removeAttribute('disabled');
                }

                grouperror.classList.add('d-none');
                group.classList.remove('is-invalid')
                return;
            }
            btn.setAttribute('disabled', 'disabled');
            grouperror.classList.remove('d-none');
            group.classList.add('is-invalid');
        }

    </script>
{% endblock %}
