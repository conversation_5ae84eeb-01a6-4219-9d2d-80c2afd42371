import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue2';
import path from 'path';
import { VitePWA } from 'vite-plugin-pwa';
import Copy from 'rollup-plugin-copy';
import { createHtmlPlugin } from 'vite-plugin-html';
import inject from '@rollup/plugin-inject';
import { viteStaticCopy } from 'vite-plugin-static-copy';

const paths = {
  build: 'public/build',
  images: 'assets/public/images/**/*',
  fonts: 'assets/public/fonts/**/*',
};

export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';

  return {
    base: '/build',
    publicDir: 'public/static',

    build: {
      outDir: paths.build,
      sourcemap: !isProduction,
      emptyOutDir: true,
      manifest: 'manifest.json',
      cssCodeSplit: true,
      minify: isProduction ? 'terser' : false,

      terserOptions: {
        ecma: 5,
        compress: {
          drop_console: isProduction,
        },
        output: {
          comments: false,
        },
      },

      rollupOptions: {
        input: {
          'public/abus/login/login': './assets/public/login/login.ts',
          'public/abus/resetPassword/resetPassword': './assets/public/resetPassword/resetPassword.ts',
          'public/abus/app/app': './assets/public/app/app.ts',
          'public/abus/layout/layout': './assets/public/layout/layout.ts',
          'public/abus/form/form': './assets/public/form/form.ts',
          'public/vendor/vendor': './assets/public/vendor/vendor.ts',
          'public/abus/register/register': './assets/public/register/register.ts',
          'protected/abus/app/appController': './assets/protected/vue/controller/appController.ts',
          'protected/abus/technicalData/technicalData': './assets/protected/technicalData/technicalData.ts',
        },

        output: {
          entryFileNames: isProduction ? 'js/[name].[hash].js' : 'js/[name].js',
          chunkFileNames: isProduction ? 'js/[name].[hash].js' : 'js/[name].js',
          assetFileNames: (assetInfo) => {
            const originalName = assetInfo.originalFileNames?.[0] || '';
            const ext = path.extname(originalName);

            // Handle images
            if (/\.(png|jpe?g|gif|svg|ico)$/.test(ext)) {
              const filePath = assetInfo.name || '';
              // Similar image path handling as in webpack config
              const assetPath = filePath
                .replace(/public(\/images)?\//, '')
                .replace('assets/', 'images/')
                .replace('node_modules/', 'images/public/node_modules/')
                .replace(/\/{2,}/, '/')
                .replace(/(?!.*\/).+/, '');

              return isProduction ? `${assetPath}[name].[hash][extname]` : `${assetPath}[name][extname]`;
            }

            // Handle fonts
            if (/\.(woff|woff2|eot|ttf|otf)$/.test(ext)) {
              return isProduction ? 'fonts/[name].[hash][extname]' : 'fonts/[name][extname]';
            }

            // Default
            return isProduction ? '[name].[hash][extname]' : '[name][extname]';
          },

          // Similar to splitEntryChunks
          advancedChunks: (id) => {
            if (id.includes('node_modules')) {
              // Handle specific problematic packages with direct paths
              if (id.includes('@fortawesome/vue-fontawesome')) {
                return 'vendor-fontawesome';
              }
              return 'vendor';
            }
          },

          globals: {
            jquery: 'jQuery',
            $: 'jQuery',
          },
        },
      },
    },

    resolve: {
      extensions: ['.ts', '.js', '.vue'],
      alias: {
        jquery: path.resolve(__dirname, 'node_modules/jquery'),
        vue$: 'vue/dist/vue.esm.js',
        vue: 'vue/dist/vue.esm.js',
        '@root': path.resolve(__dirname),
        '@assets': path.resolve(__dirname, 'assets/'),
        '@node_modules': path.resolve(__dirname, 'node_modules/'),
        Assets: path.resolve(__dirname, 'assets/'),
        Translations: path.resolve(__dirname, 'translations/'),
        '@translations': path.resolve(__dirname, 'translations/'),
        EventBuses: path.resolve(__dirname, 'assets/protected/vue/eventBuses/'),
        Flags: path.resolve(__dirname, 'node_modules/svg-country-flags/'),
        '@flags': path.resolve(__dirname, 'node_modules/svg-country-flags/'),
        'eonasdan-bootstrap-datetimepicker': path.resolve(
          __dirname,
          'node_modules/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min.js',
        ),
        'vue-bootstrap-datetimepicker-css': path.resolve(
          __dirname,
          'node_modules/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css',
        ),
        // Fix for @fortawesome/vue-fontawesome
        '@fortawesome/vue-fontawesome': path.resolve(__dirname, 'node_modules/@fortawesome/vue-fontawesome/index.js'),
        // Add aliases for bootstrap SCSS imports with tilde
        '~bootstrap': path.resolve(__dirname, 'node_modules/bootstrap'),
        bootstrap: path.resolve(__dirname, 'node_modules/bootstrap'),
        '~bootstrap-vue': path.resolve(__dirname, 'node_modules/bootstrap-vue'),
        'bootstrap-vue': path.resolve(__dirname, 'node_modules/bootstrap-vue'),
        // Generic tilde alias
        '~': path.resolve(__dirname, 'node_modules'),
      },
    },

    css: {
      preprocessorOptions: {
        scss: {
          // No additionalData here since we want to directly import
          // the Bootstrap files in each SCSS file as in the original
          includePaths: [path.resolve(__dirname, 'node_modules')],
          sourceMap: !isProduction,
        },
        less: {
          javascriptEnabled: true,
        },
      },
    },

    plugins: [
      inject({
        $: 'jquery',
        jQuery: 'jquery',
      }),

      vue({
        // Vue2 options
        template: {
          compilerOptions: {
            whitespace: 'preserve',
          },
        },
      }),

      // Babel/TypeScript configuration
      // legacy({
      //   targets: ['> 1%', 'ie 9-11'],
      //   additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      // }),

      // Handle tilde imports in SCSS
      {
        name: 'scss-transform',
        enforce: 'pre',
        transform(code, id) {
          if (id.endsWith('.scss') || id.endsWith('.sass')) {
            return {
              code: code.replace(/~(bootstrap|bootstrap-vue|.*?)\//g, (match, pkg) => {
                return `${pkg}/`;
              }),
              map: null,
            };
          }
        },
      },

      // Enable PWA (workbox) - equivalent to GenerateSW plugin
      VitePWA({
        registerType: 'autoUpdate',
        workbox: {
          clientsClaim: true,
          skipWaiting: true,
          maximumFileSizeToCacheInBytes: 10000000,
        },
      }),

      Copy({
        targets: [
          {
            src: paths.images,
            dest: `${paths.build}/images`,
            rename: (_name, _extension, fullpath) => {
              const keptParts = fullpath.split(path.sep).slice(3);
              return path.join(...keptParts);
            },
          },
          {
            src: paths.fonts,
            dest: `${paths.build}/fonts`,
          },
        ],
        hook: 'writeBundle',
      }),

      viteStaticCopy({
        targets: [
          {
            src: 'node_modules/svg-country-flags/png100px/*',
            dest: 'images/public/node_modules/svg-country-flags/png100px',
          },
        ],
      }),

      // HTML plugin for potential entry points
      createHtmlPlugin({
        minify: isProduction,
        inject: {
          data: {
            // Any data you want to inject into HTML templates
            title: 'ABUS Portal',
          },
        },
      }),
    ],

    // Dev server configuration
    server: {
      port: 8080,
      strictPort: true,
      hmr: true,
    },

    // Optimization
    optimizeDeps: {
      include: ['jquery', 'vue', 'lodash', 'bootstrap', 'moment', 'axios', 'vue-i18n'],
      rollupOptions: {
        // Add external globals for browser-only packages
        define: {
          global: 'window',
        },
      },
    },
  };
});
