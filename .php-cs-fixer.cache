{"php": "7.4.26", "version": "3.3.2:v3.3.2#06bdbdfcd619183dd7a1a6948360f8af73b9ecec", "indent": "    ", "lineEnding": "\n", "rules": {"list_syntax": true, "visibility_required": true, "ternary_to_null_coalescing": true, "array_syntax": true, "php_unit_dedicate_assert_internal_type": {"target": "7.5"}, "php_unit_namespaced": {"target": "6.0"}, "php_unit_dedicate_assert": {"target": "5.6"}, "php_unit_expectation": {"target": "5.6"}, "php_unit_mock": {"target": "5.5"}, "php_unit_no_expectation_annotation": {"target": "4.3"}, "backtick_to_shell_exec": true, "binary_operator_spaces": true, "blank_line_before_statement": {"statements": ["return"]}, "braces": {"allow_single_line_anonymous_class_with_empty_body": true, "allow_single_line_closure": true}, "cast_spaces": true, "class_attributes_separation": {"elements": {"method": "one"}}, "class_definition": {"single_line": true}, "clean_namespace": true, "concat_space": true, "echo_tag_syntax": true, "empty_loop_body": {"style": "braces"}, "empty_loop_condition": true, "fully_qualified_strict_types": true, "function_typehint_space": true, "general_phpdoc_tag_rename": {"replacements": {"inheritDocs": "inheritDoc"}}, "include": true, "increment_style": true, "integer_literal_case": true, "lambda_not_used_import": true, "linebreak_after_opening_tag": true, "magic_constant_casing": true, "magic_method_casing": true, "method_argument_space": {"on_multiline": "ignore"}, "native_function_casing": true, "native_function_type_declaration_casing": true, "no_alias_language_construct_call": true, "no_alternative_syntax": true, "no_binary_string": true, "no_blank_lines_after_phpdoc": true, "no_empty_comment": true, "no_empty_phpdoc": true, "no_empty_statement": true, "no_extra_blank_lines": {"tokens": ["case", "continue", "curly_brace_block", "default", "extra", "parenthesis_brace_block", "square_brace_block", "switch", "throw", "use"]}, "no_leading_namespace_whitespace": true, "no_mixed_echo_print": true, "no_multiline_whitespace_around_double_arrow": true, "no_short_bool_cast": true, "no_singleline_whitespace_before_semicolons": true, "no_spaces_around_offset": true, "no_superfluous_phpdoc_tags": {"allow_mixed": true, "allow_unused_params": true}, "no_trailing_comma_in_list_call": true, "no_trailing_comma_in_singleline_array": true, "no_unneeded_control_parentheses": {"statements": ["break", "clone", "continue", "echo_print", "return", "switch_case", "yield", "yield_from"]}, "no_unneeded_curly_braces": {"namespaces": true}, "no_unset_cast": true, "no_unused_imports": true, "no_whitespace_before_comma_in_array": true, "normalize_index_brace": true, "object_operator_without_whitespace": true, "ordered_imports": true, "php_unit_fqcn_annotation": true, "php_unit_method_casing": true, "phpdoc_align": true, "phpdoc_annotation_without_dot": true, "phpdoc_indent": true, "phpdoc_inline_tag_normalizer": true, "phpdoc_no_access": true, "phpdoc_no_alias_tag": true, "phpdoc_no_package": true, "phpdoc_no_useless_inheritdoc": true, "phpdoc_return_self_reference": true, "phpdoc_scalar": true, "phpdoc_separation": true, "phpdoc_single_line_var_spacing": true, "phpdoc_summary": true, "phpdoc_tag_type": {"tags": {"inheritDoc": "inline"}}, "phpdoc_to_comment": true, "phpdoc_trim": true, "phpdoc_trim_consecutive_blank_line_separation": true, "phpdoc_types": true, "phpdoc_types_order": {"null_adjustment": "always_last", "sort_algorithm": "none"}, "phpdoc_var_without_name": true, "semicolon_after_instruction": true, "single_class_element_per_statement": true, "single_line_comment_style": {"comment_types": ["hash"]}, "single_line_throw": true, "single_quote": true, "single_space_after_construct": true, "space_after_semicolon": {"remove_in_empty_for_expressions": true}, "standardize_increment": true, "standardize_not_equals": true, "switch_continue_to_break": true, "trailing_comma_in_multiline": true, "trim_array_spaces": true, "types_spaces": true, "unary_operator_spaces": true, "whitespace_after_comma_in_array": true, "yoda_style": true, "blank_line_after_opening_tag": true, "compact_nullable_typehint": true, "declare_equal_normalize": true, "lowercase_cast": true, "lowercase_static_reference": true, "new_with_braces": true, "no_blank_lines_after_class_opening": true, "no_leading_import_slash": true, "no_whitespace_in_blank_line": true, "ordered_class_elements": {"order": ["use_trait"]}, "return_type_declaration": true, "short_scalar_cast": true, "single_blank_line_before_namespace": true, "single_trait_insert_per_statement": true, "ternary_operator_spaces": true, "blank_line_after_namespace": true, "constant_case": true, "elseif": true, "function_declaration": true, "indentation_type": true, "line_ending": true, "lowercase_keywords": true, "no_break_comment": true, "no_closing_tag": true, "no_space_around_double_colon": true, "no_spaces_after_function_name": true, "no_spaces_inside_parenthesis": true, "no_trailing_whitespace": true, "no_trailing_whitespace_in_comment": true, "single_blank_line_at_eof": true, "single_import_per_statement": true, "single_line_after_imports": true, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "encoding": true, "full_opening_tag": true, "array_push": true, "combine_nested_dirname": true, "dir_constant": true, "ereg_to_preg": true, "error_suppression": true, "fopen_flag_order": true, "fopen_flags": {"b_mode": false}, "function_to_constant": true, "implode_call": true, "is_null": true, "logical_operators": true, "modernize_types_casting": true, "native_constant_invocation": true, "native_function_invocation": {"include": ["@compiler_optimized"], "scope": "namespaced", "strict": true}, "no_alias_functions": true, "no_homoglyph_names": true, "no_php4_constructor": true, "no_unneeded_final_method": true, "no_useless_sprintf": true, "non_printable_character": true, "ordered_traits": true, "php_unit_construct": true, "php_unit_mock_short_will_return": true, "php_unit_set_up_tear_down_visibility": true, "php_unit_test_annotation": true, "psr_autoloading": true, "self_accessor": true, "set_type_to_cast": true, "string_length_to_empty": true, "string_line_ending": true, "ternary_to_elvis_operator": true, "pow_to_exponentiation": true, "no_trailing_whitespace_in_string": true, "nullable_type_declaration_for_default_null_value": {"use_nullable_type_declaration": false}}, "hashes": {"src/Workflow/Information/GuardListener.php": 4270660487, "src/Model/#Usermanagement/ResponseObject/CompanySummaryResponseObject.php": 4095479032, "src/Model/#Usermanagement/ResponseObject/CompanyListResponseObject.php": 3875271409, "src/Model/#Usermanagement/ResponseObject/PermissionPackageListResponseObject.php": *********, "src/Model/#Usermanagement/UserManagementRepository.php": 3356968523, "src/Model/FormsReporting/FormsReporting.php": *********, "src/Model/APIS/InformationBuilder/InformationBuilderInterface.php": 1451964899, "src/Model/APIS/InformationBuilder/FilePurifier.php": *********, "src/Model/APIS/InformationBuilder/InformationPurifierInterface.php": 1036196047, "src/Model/APIS/InformationBuilder/Extractor/ExtractorReason.php": 2857609184, "src/Model/APIS/InformationBuilder/Extractor/ExtractedReasonObject.php": 3343183616, "src/Model/APIS/InformationBuilder/Extractor/ExtractedContentObject.php": 68339809, "src/Model/APIS/InformationBuilder/Extractor/ExtractorContent.php": 1615524014, "src/Model/APIS/InformationBuilder/InformationBuilderABUS.php": 3576482232, "src/Model/APIS/InformationBuilder/InformationPurifier.php": 3497516142, "src/Model/APIS/InformationBuilder/ReferencePurifierInterface.php": 1785286543, "src/Model/APIS/InformationBuilder/FilePurifierInterface.php": 3385325111, "src/Model/APIS/InformationBuilder/ReferencePurifier.php": 3795924425, "src/Model/APIS/Configuration/SectionObject.php": 3893697787, "src/Model/APIS/Configuration/ConfigurationGetterInterface.php": 3848716335, "src/Model/APIS/Configuration/ConfigurationObject.php": 1873608072, "src/Model/APIS/Configuration/ConfigurationGetterYML.php": 1095579776, "src/Model/APIS/Configuration/RolesObject.php": 2987951287, "src/Model/APIS/Serializer/Serializer.php": 389957972, "src/Model/APIS/Permission/PermissionCheckerInterface.php": 2758960664, "src/Model/APIS/Permission/PermissionChecker.php": 1526670899, "src/Model/APIS/Storer/StorerDatabase.php": 3675316905, "src/Model/APIS/Storer/StorerInterface.php": 3396748788, "src/Model/APIS/Loader/LoaderDatabase.php": 3173031367, "src/Model/APIS/Loader/LoaderInterface.php": 1942053718, "src/Model/APIS/Remover/RemoverDatabase.php": 760423076, "src/Model/APIS/Remover/RemoverInterface.php": 3390055896, "src/Model/APIS/Repository/RepositoryDatabase.php": 1929938958, "src/Model/APIS/Repository/RepositoryInterface.php": 2767109879, "src/Model/APIS/Handler/ReceiverList/ReceiverListHandler.php": 215956773, "src/Model/APIS/Handler/Dashboard/DashboardHandler.php": 1118950079, "src/Model/APIS/Handler/Settings/SettingsHandler.php": 1820765238, "src/Model/APIS/Handler/Report/ReportHandler.php": 4108125298, "src/Model/APIS/Handler/Reference/ReferenceHandler.php": 266206733, "src/Model/APIS/Handler/Uploader/UploadHandler.php": 10128441, "src/Model/APIS/Handler/Transition/TransitionHandler.php": 849044422, "src/Model/APIS/Handler/MarkedAsRead/MarkedAsReadHandler.php": 1629455313, "src/Model/APIS/Handler/Information/InformationHandler.php": 1769218338, "src/Model/APIS/Handler/Mailer/MailerHandler.php": 720651501, "src/Model/APIS/Handler/Favorite/FavoriteHandler.php": 4164127998, "src/Model/APIS/Handler/Viewed/ViewedHandler.php": 4033060041, "src/Model/APIS/Handler/ReleaseByCronjob/ReleaseByCronjob.php": 633631652, "src/Model/Box/Configuration/Helper/ConfigurationObjectArrayHelper.php": 915649738, "src/Model/Box/Configuration/Parts/DisplayObject.php": 192283107, "src/Model/Box/Configuration/Parts/LanguageObject.php": 3855390619, "src/Model/Box/Configuration/Parts/SearchObject.php": 537189400, "src/Model/Box/Configuration/Parts/PermissionObject.php": 1565213077, "src/Model/Box/Configuration/Parts/MetaObject.php": 3735663263, "src/Model/Box/Configuration/ConfigurationObject.php": 3719345733, "src/Model/Box/Helper/PathHelper.php": 2128700449, "src/Model/Box/Helper/ValueHelper.php": 3920630628, "src/Model/Box/ConfigurationParser/ConfigurationParserDatabase.php": 2344446592, "src/Model/Box/ConfigurationParser/ConfigurationParserInterface.php": 2900077572, "src/Model/Box/ConfigurationParser/ConfigurationParserFilesystem.php": 3073315035, "src/Model/Box/Upload/FolderOperations.php": 3835744886, "src/Model/Box/TaskRunner/TaskRunner.php": 4166597972, "src/Model/Box/Permission/PermissionChecker.php": 3608457461, "src/Model/Box/Loader/LoaderDatabase.php": 3172171874, "src/Model/Box/Loader/LoaderInterface.php": 1258640996, "src/Model/Box/ConfigurationMerger/ConfigurationMerger.php": 734650139, "src/Model/Box/Nextcloud/Elasticsearch/Enricher.php": 284076276, "src/Model/Box/Nextcloud/ConfigParser/ConfigParser.php": 2418458921, "src/Model/Box/Nextcloud/Connector/WebdavConnectorInterface.php": 2956344483, "src/Model/Box/Nextcloud/Connector/FileSystemConnector.php": 2733228778, "src/Model/Box/Nextcloud/Connector/ElasticSearch/ElasticSearchConnector.php": 624484641, "src/Model/Box/Nextcloud/Connector/Webdav/WebdavConnectorGuzzleConfigParser.php": 282640468, "src/Model/Box/Nextcloud/Connector/Webdav/WebdavConnectorGuzzle.php": 1686150814, "src/Model/Box/Nextcloud/Connector/Webdav/XmlToArray.php": 836493688, "src/Model/Box/Nextcloud/FileZipper/FileZipper.php": 123347359, "src/Model/Box/Nextcloud/Permission/PermissionChecker.php": 1212920186, "src/Model/Box/Nextcloud/DTO/ConfigTreeItem.php": 2184288000, "src/Model/Box/Nextcloud/DTO/Configuration.php": 2454312536, "src/Model/Box/Nextcloud/ConfigBuilder/Merger.php": 1593630461, "src/Model/Box/Nextcloud/ConfigBuilder/Enricher.php": 1217099583, "src/Model/Box/Nextcloud/ConfigBuilder/IdentifierGenerator.php": 1838319121, "src/Model/Box/Nextcloud/TempCleaner/TempCleaner.php": 3632699543, "src/Model/Box/Nextcloud/Tagger/Tagger.php": 1735632278, "src/Model/Box/Nextcloud/Database/Portal/PortalDatabaseConnectorMySQL.php": 2532459138, "src/Model/Box/Nextcloud/Database/Portal/PortalDatabaseConnectorInterface.php": 3964180746, "src/Model/Box/Nextcloud/Database/Nextcloud/NextcloudDatabaseConnectorMySQL.php": 1082118702, "src/Model/Box/Nextcloud/Database/Nextcloud/NextcloudDatabaseConnectorInterface.php": 4263359883, "src/Model/Box/Nextcloud/inMemory/inMemoryConnectorRedis.php": 59678978, "src/Model/Box/Nextcloud/inMemory/inMemoryConnectorInterface.php": 17951945, "src/Model/Box/Download/Download.php": 303809029, "src/Model/Box/DatabaseOperations/DatabaseIndexUpdater.php": 1137293724, "src/Model/Box/DatabaseOperations/FilesystemIndexUpdater.php": 744947685, "src/Model/Box/DatabaseOperations/DatabaseOperations.php": 1624941445, "src/Model/Box/Crawler/Statistics/CrawlerStatistics.php": 710881265, "src/Model/Box/Crawler/PDFParser/PDFParser.php": 1183543794, "src/Model/Box/Crawler/Configuration/CrawlerConfiguration.php": 1331924666, "src/Model/Box/Crawler/Helper/StringCleaner.php": 4042583991, "src/Model/Box/Crawler/Crawler.php": 2632782924, "src/Model/Box/Content/Loader.php": 2484682600, "src/Model/Box/Content/ContentObject.php": 1080427332, "src/Model/Box/Content/ContentShowObject.php": 3914724611, "src/Model/Box/Content/ContentLanguageObject.php": 3980058281, "src/Model/Box/Content/Processor.php": 2997779007, "src/Model/Box/Content/ContentMetaObject.php": 679220711, "src/Model/Box/Content/StoreHandler.php": 1098314557, "src/Model/Box/FilesystemOperations/FilesystemOperations.php": 2316378071, "src/Model/Box/FilesystemOperations/FilesystemOperatorFacade.php": 2835600024, "src/Model/Service/Service.php": 2977869841, "src/Model/Service/GoogleMapsDistanceMatrix.php": 4211210508, "src/Model/ABUSVital/ABUSVital.php": 4074861592, "src/Model/TechnicalData/TechnicalData.php": 1456289930, "src/Model/TechnicalData/DownloadLogger.php": 2444787515, "src/Model/ABUTools/Response/XML/XMLResponse.php": 476324784, "src/Model/ABUTools/Response/XML/XMLResponseInterface.php": 2296728670, "src/Model/ABUTools/Response/JSON/JSONResponseInterface.php": 3503192975, "src/Model/ABUTools/Response/JSON/JSONResponse.php": 1644457065, "src/Model/ABUTools/Response/ResponseInterface.php": 670703703, "src/Model/ABUTools/Authorization/AuthorizationInterface.php": 2274437660, "src/Model/ABUTools/Authorization/AuthorizationLDAP.php": 565435711, "src/Model/ABUTools/Data/DataGetterInterface.php": 2277574477, "src/Model/ABUTools/Data/Auftragsdaten/KundeData.php": 2146845859, "src/Model/ABUTools/Data/Auftragsdaten/AuftragsdatenData.php": 2125483309, "src/Model/ABUTools/Data/Auftragsdaten/Getter/AuftragsdatenGetter.php": 570710193, "src/Model/ABUTools/Data/Group/GroupFilterInterface.php": 1604846182, "src/Model/ABUTools/Data/Group/GroupFilter.php": 3979056420, "src/Model/ABUTools/Data/Group/GroupsData.php": 3975833978, "src/Model/ABUTools/Data/Version/SoftwareVersionData.php": 3677377717, "src/Model/ABUTools/Data/Version/Checker/VersionCheckerInterface.php": 144674918, "src/Model/ABUTools/Data/Version/Checker/VersionChecker.php": 1119552548, "src/Model/ABUTools/Data/Version/Getter/VersionGetterIchWeißNochNichtWie.php": 3617976067, "src/Model/ABUTools/Data/Version/Getter/VersionGetterForTesting.php": 919681726, "src/Model/ABUTools/Data/DataInterface.php": 2607621795, "src/Model/TaskRunner/TaskCreator.php": 3303879072, "src/Model/TaskRunner/TaskPermission.php": 799381498, "src/Model/TaskRunner/TaskMailer.php": 1736055175, "src/Model/TaskRunner/TaskPostData.php": 2878960075, "src/Model/TaskRunner/TaskRunnerInterface.php": 3035446484, "src/Model/TaskRunner/#TaskRunner.php": 2434263746, "src/Model/TaskRunner/TaskExecutor.php": 3492029178, "src/Model/TaskRunner/TaskPostDataAnalyzer.php": 2138450410, "src/Model/Reporting/Reporting.php": 6362880, "src/Model/Alert/Alert.php": 1186779855, "src/Model/CRM/Connector/CRMConnectorInterface.php": 767506619, "src/Model/CRM/Connector/CRMConnectorSuiteCRM.php": 1632685968, "src/Model/News/Serializer/Serializer.php": 569464355, "src/Model/News/Sanitizer/Sanitizer.php": 3791211851, "src/Model/News/Permission/PermissionCheckerInterface.php": 1811115622, "src/Model/News/Permission/PermissionChecker.php": 1564979743, "src/Model/News/Storer/StorerDatabase.php": 2960969469, "src/Model/News/Storer/StorerInterface.php": 3406245601, "src/Model/News/Loader/LoaderDatabase.php": 3491257277, "src/Model/News/Loader/LoaderInterface.php": 2459121440, "src/Model/News/GroupGetter.php": 146550684, "src/Model/News/Remover/RemoverDatabase.php": 3346912108, "src/Model/News/Remover/RemoverInterface.php": 1064824077, "src/Model/News/Handler/Uploader/UploadHandler.php": 507230876, "src/Model/News/Handler/News/NewsHandler.php": 4231990916, "src/Model/News/ModuleGetter.php": 942541755, "src/Model/News/NewsBuilder/NewsBuilder.php": 1321281369, "src/Model/News/NewsBuilder/FilePurifier.php": 406070974, "src/Model/News/NewsBuilder/NewsPurifier.php": 201992008, "src/Model/News/NewsBuilder/NewsPurifierInterface.php": 858523490, "src/Model/News/NewsBuilder/FilePurifierInterface.php": 569632024, "src/Model/News/NewsBuilder/NewsBuilderInterface.php": 2932301079, "src/Model/CADExchange/Connector/Webdav/WebdavConnectorInterface.php": 81418620, "src/Model/CADExchange/Connector/Webdav/Guzzle/WebdavConnectorGuzzle.php": 3504775211, "src/Model/CADExchange/Connector/Nextcloud/NextcloudConnectorInterface.php": 1586095275, "src/Model/CADExchange/Connector/Nextcloud/Guzzle/NextcloudConnectorGuzzle.php": 434017015, "src/Model/CADExchange/Connector/Database/SQL/DatabaseConnectorSQL.php": 750543103, "src/Model/CADExchange/Connector/Database/DatabaseConnectorInterface.php": 684873935, "src/Model/CADExchange/Connector/inMemory/inMemoryConnectorInterface.php": 2979218220, "src/Model/CADExchange/Connector/inMemory/Redis/inMemoryConnectorRedis.php": 4107159648, "src/Model/CADExchange/Config/ConfigReader.php": 4235163764, "src/Model/CADExchange/Config/ConfigObject.php": 2242206657, "src/Model/CADExchange/Mailer/Mailer.php": 1927622675, "src/Model/CADExchange/Crawler/Crawler.php": 1454153764, "src/Model/FamilyDay/FamilyDay.php": 1803798145, "src/Model/Management/User/Loader/Redis/LoaderRedis.php": 3393543631, "src/Model/Management/User/Loader/LoaderInterface.php": 2108370274, "src/Model/Management/LDAPProblemFinder.php": 1335604333, "src/Model/Api/Connector/LDAP/ConnectionAttributesLDAP.php": *********, "src/Model/Api/Connector/LDAP/ConnectorLDAP.php": *********, "src/Model/Api/Connector/inMemoryConnectorInterface.php": 3240962850, "src/Model/Api/Connector/Redis/RedisConnector.php": 1090634592, "src/Model/Api/#User/AttributeRepository/AttributeRepositoryLDAP.php": 1755931031, "src/Model/Api/#User/AttributeRepository/AttributeRepositoryInterface.php": 4073844663, "src/Model/Api/ABUKonfis/ABUKonfis.php": 1664451490, "src/Model/Api/LDAP2Redis/LDAP2Redis.php": 4211960374, "src/Model/Api/#CompanyCategory/LDAP/CompanyCategoryReadRepositoryLDAP.php": *********, "src/Model/Api/#CompanyCategory/LDAP/CompanyCategoryLDAP.php": *********, "src/Model/Api/#CompanyCategory/CompanyCategoryObject.php": *********, "src/Model/Api/#CompanyCategory/CompanyCategoryWriteRepositoryInterface.php": 3038824685, "src/Model/Api/#CompanyCategory/CompanyCategoryInterface.php": 4096542510, "src/Model/Api/#CompanyCategory/Redis/CompanyCategoryRedis.php": 2028761575, "src/Model/Api/#CompanyCategory/Redis/CompanyCategoryWriteRepositoryRedis.php": *********, "src/Model/Api/#CompanyCategory/Redis/CompanyCategoryReadRepositoryRedis.php": 2995537994, "src/Model/Api/#CompanyCategory/CompanyCategoryReadRepositoryInterface.php": 1387956852, "src/Model/Api/FileOperation/FileHandlerInterface.php": 4087398681, "src/Model/Api/FileOperation/FileHandler.php": 2698070541, "src/Model/Api/Portal/PortalRepository.php": *********, "src/Model/Api/Generator/RandomHashGenerator.php": *********, "src/Model/Api/VideoConference/VideoConference.php": *********, "src/Model/Api/VideoConference/VideoConferenceAccess.php": 35734145, "src/Model/Api/VideoConference/JitsiConferenceAccess.php": 4229086433, "src/Model/Api/##User/LDAP/UserWritePermissionLDAP.php": 2700276916, "src/Model/Api/##User/LDAP/UserReadRepositoryLDAP.php": 2087404023, "src/Model/Api/##User/LDAP/UserLDAP.php": 1939557738, "src/Model/Api/##User/LDAP/UserWriteRepositoryLDAP.php": 3944113604, "src/Model/Api/##User/UserWritePermissionInterface.php": 2046043368, "src/Model/Api/##User/UserObject.php": 4028165678, "src/Model/Api/##User/UserInterface.php": 1136911206, "src/Model/Api/##User/UserWriteRepositoryInterface.php": *********, "src/Model/Api/##User/UserReadRepositoryInterface.php": *********, "src/Model/Api/##User/Redis/UserReadRepositoryRedis.php": 3384997370, "src/Model/Api/##User/Redis/UserRedis.php": 4223989081, "src/Model/Api/##User/Redis/UserWriteRepositoryRedis.php": 2388310082, "src/Model/Api/Functions.php": 2935135121, "src/Model/Api/#UserManagement/CompanyResponsibilityRepository.php": 3102834734, "src/Model/Api/#UserManagement/PermissionPackageRepository.php": 2931532294, "src/Model/Api/#UserManagement/PermissionChecker.php": 4087397056, "src/Model/Api/#UserManagement/CompanyRepository.php": 2515514142, "src/Model/Api/#UserManagement/Configuration.php": 4201107159, "src/Model/Api/TechnicalData/TechnicalDataRepository.php": 3376273894, "src/Model/Api/Language/LanguageSanitizer.php": 2128046005, "src/Model/Api/#Group/LDAP/GroupReadRepositoryLDAP.php": 1107063213, "src/Model/Api/#Group/LDAP/GroupLDAP.php": *********, "src/Model/Api/#Group/LDAP/GroupWriteRepositoryLDAP.php": 4008900136, "src/Model/Api/#Group/GroupInterface.php": 3678392295, "src/Model/Api/#Group/GroupReadRepositoryInterface.php": 2012906050, "src/Model/Api/#Group/GroupObject.php": 1753162893, "src/Model/Api/#Group/Redis/GroupRedis.php": 3588398039, "src/Model/Api/#Group/Redis/GroupReadRepositoryRedis.php": *********, "src/Model/Api/#Group/Redis/GroupWriteRepositoryRedis.php": 2655891743, "src/Model/Api/#Group/GroupWriteRepositoryInterface.php": 1738141785, "src/Model/Api/UserMode/LDAP/UserModeLDAP.php": 4079554056, "src/Model/Api/UserMode/UserModeInterface.php": *********, "src/Model/Api/UserMode/Redis/UserModeRedis.php": 4184320631, "src/Model/Api/CatalogCreator/CatalogCreatorRepository.php": 798167986, "src/Model/Api/CatalogCreator/Catalog.php": 1584770703, "src/Model/Api/CatalogCreator/CatalogCreator.php": 1979594374, "src/Model/Api/SingleSignOn/MatomoSingleSignOn.php": 3571482338, "src/Model/Api/SingleSignOn/MediaSingleSignOn.php": 1104051388, "src/Model/Api/SingleSignOn/Helper/SingleSignOnHelper.php": 2351905244, "src/Model/Api/SingleSignOn/CatalogCreatorSingleSignOn.php": 2854439515, "src/Model/Api/SingleSignOn/SingleSignOn.php": 2807131282, "src/Model/Api/SingleSignOn/GitlabSingleSignOn.php": 402287198, "src/Model/Api/SingleSignOn/SingleSignOnInterface.php": 4272511362, "src/Model/Api/SingleSignOn/NextcloudSingleSignOn.php": 694755293, "src/Model/Api/SingleSignOn/LegacyPortalSingleSignOn.php": 4155821091, "src/Model/Api/Gitlab/Gitlab.php": 3003787082, "src/Model/Api/Facade/FilesystemOperatorFacade.php": 2059232346, "src/Model/Api/Facade/BinaryFileResponseFacade.php": 1025733459, "src/Model/Api/Facade/cURLFacade.php": 4071143944, "src/Model/Api/Facade/#FunctionsFacade.php": 4014419243, "src/Model/Api/Facade/DateTimeFacade.php": 697927682, "src/Model/Api/Facade/MicrotimeFacade.php": 730310011, "src/Model/Api/Facade/SplFileInfoFacade.php": 3148166054, "src/Model/Api/Facade/GDFacade.php": 2691000599, "src/Model/Api/PasswordManagement/ChangePassword.php": 3090437359, "src/Model/Api/PasswordManagement/UserPasswordRecovery.php": 3162710614, "src/Model/Api/PasswordManagement/UniqueForgotPasswordHash.php": 2534426523, "src/Model/Api/PasswordManagement/PasswordSecurity/LdapPasswordSecurity.php": 439642585, "src/Model/Api/PasswordManagement/PasswordSecurity/PasswordSecurityInterface.php": 1409039349, "src/Model/Api/PasswordManagement/ResetPassword.php": 1449459877, "src/Model/Api/PasswordManagement/DatabaseOperations/ResetPasswordOperations.php": 3138344889, "src/Model/Api/Summernote/ImageUploader.php": 23339264, "src/Model/Api/Logger/Constants/LoggerValue.php": 2571047595, "src/Model/Api/Logger/Constants/LoggerTopic.php": 3577358944, "src/Model/Api/Logger/Constants/LoggerKey.php": 213186482, "src/Model/Api/Logger/Logger.php": 888812818, "src/Model/Api/Logger/LoggerInterface.php": 3578822196, "src/Model/Api/PortalUser/PortalUser.php": 2931891339, "src/Model/Api/PortalUser/PortalUserInterface.php": 1364566855, "src/Model/Api/Mimetype/Mimetype2Extension.php": 1138317605, "src/Model/Api/Mailer/MailerInterface.php": 3113424751, "src/Model/Api/Mailer/MailerSwift.php": *********, "src/Model/Api/Mailer/TwigMailer.php": 1670353064, "src/Model/Api/Module/ModuleInterface.php": 3213142748, "src/Model/Api/Module/Module.php": 4011277812, "src/Model/Api/Module/ModuleGetterYML.php": 1999394659, "src/Model/Api/Module/ModuleGetterInterface.php": 3636479971, "src/Model/Api/Module/ModuleRepository.php": 1059189548, "src/Model/Api/Module/ModuleRepositoryInterface.php": 3065765950, "src/Model/Api/#Company/LDAP/CompanyLDAP.php": 3997759511, "src/Model/Api/#Company/LDAP/CompanyWriteRepositoryLDAP.php": 4282375115, "src/Model/Api/#Company/LDAP/CompanyReadRepositoryLDAP.php": 2898787445, "src/Model/Api/#Company/LDAP/#CompaniesRepositoryLDAP.php": *********, "src/Model/Api/#Company/#CompanyRepositoryInterface.php": 2230540950, "src/Model/Api/#Company/CompanyObject.php": *********, "src/Model/Api/#Company/CompanyInterface.php": 2534493423, "src/Model/Api/#Company/CompanyReadRepositoryInterface.php": 1039826001, "src/Model/Api/#Company/Redis/CompanyWriteRepositoryRedis.php": 2614222836, "src/Model/Api/#Company/Redis/CompanyReadRepositoryRedis.php": *********, "src/Model/Api/#Company/Redis/#CompaniesRepositoryRedis.php": 1405294914, "src/Model/Api/#Company/Redis/CompanyRedis.php": *********, "src/Model/Api/#Company/CompanyWriteRepositoryInterface.php": 3446106914, "src/Model/Api/Setting/SettingRepository.php": *********, "src/Model/Api/Setting/SettingRepositoryInterface.php": *********, "src/Model/Api/Werksvertretung/WerksvertretungRepository.php": 3052300992, "src/Model/Api/Werksvertretung/Werksvertretung.php": 1011301582, "src/Model/Api/UserManagement/Options/Options.php": *********, "src/Model/Api/UserManagement/EntryConverter/EntryConverter.php": 3556462131, "src/Model/Api/UserManagement/ObjectBuilder/UserBuilder.php": 4033853596, "src/Model/Api/UserManagement/ObjectBuilder/CompanyBuilder.php": 1690413753, "src/Model/Api/UserManagement/Serializer/NameConverter.php": 2812435381, "src/Model/Api/UserManagement/Serializer/UserManagementSerializer.php": *********, "src/Model/Api/UserManagement/Regex/Regex.php": 1578109390, "src/Model/Api/UserManagement/User/LDAP/UserReadRepositoryLDAP.php": *********, "src/Model/Api/UserManagement/User/LDAP/UserWriteRepositoryLDAP.php": *********, "src/Model/Api/UserManagement/User/User.php": *********, "src/Model/Api/UserManagement/User/UserWriteRepositoryInterface.php": 4264685846, "src/Model/Api/UserManagement/User/UserReadRepositoryInterface.php": 2510530501, "src/Model/Api/UserManagement/User/Redis/UserReadRepositoryRedis.php": *********, "src/Model/Api/UserManagement/User/Redis/UserWriteRepositoryRedis.php": *********, "src/Model/Api/UserManagement/PermissionChecker/PermissionChecker.php": 3581583366, "src/Model/Api/UserManagement/ResponseObject/CompanySummaryResponseObject.php": 3738535287, "src/Model/Api/UserManagement/ResponseObject/CompanyListResponseObject.php": 3973544731, "src/Model/Api/UserManagement/ResponseObject/PermissionPackageListResponseObject.php": 1676327381, "src/Model/Api/UserManagement/Facade/LDAP/UserManagementWriteLDAP.php": 1246105670, "src/Model/Api/UserManagement/Facade/LDAP/UserManagementReadLDAP.php": 1286038414, "src/Model/Api/UserManagement/Facade/UserManagementReadInterface.php": 4013581185, "src/Model/Api/UserManagement/Facade/UserManagementWriteInterface.php": 3169505461, "src/Model/Api/UserManagement/Facade/Redis/UserManagementWriteRedis.php": 2084420529, "src/Model/Api/UserManagement/Facade/Redis/UserManagementReadRedis.php": 2521894667, "src/Model/Api/UserManagement/Group/LDAP/GroupReadRepositoryLDAP.php": *********, "src/Model/Api/UserManagement/Group/LDAP/GroupWriteRepositoryLDAP.php": 3854755590, "src/Model/Api/UserManagement/Group/Group.php": 4260435672, "src/Model/Api/UserManagement/Group/GroupReadRepositoryInterface.php": *********, "src/Model/Api/UserManagement/Group/Redis/GroupReadRepositoryRedis.php": 2886203661, "src/Model/Api/UserManagement/Group/Redis/GroupWriteRepositoryRedis.php": 1617445463, "src/Model/Api/UserManagement/Group/GroupWriteRepositoryInterface.php": *********, "src/Model/Api/UserManagement/UserManagementRepository.php": 2298439830, "src/Model/Api/UserManagement/Identifier/IdentifierGetter.php": 4218561880, "src/Model/Api/UserManagement/DatabaseConnector/Country.php": 4294539010, "src/Model/Api/UserManagement/DatabaseConnector/Werksvertretung.php": *********, "src/Model/Api/UserManagement/Company/LDAP/CompanyWriteRepositoryLDAP.php": 3945544969, "src/Model/Api/UserManagement/Company/LDAP/CompanyReadRepositoryLDAP.php": 2913516561, "src/Model/Api/UserManagement/Company/CompanyReadRepositoryInterface.php": 2217094801, "src/Model/Api/UserManagement/Company/Company.php": 3637743205, "src/Model/Api/UserManagement/Company/Redis/CompanyWriteRepositoryRedis.php": 1645550859, "src/Model/Api/UserManagement/Company/Redis/CompanyReadRepositoryRedis.php": 1976453270, "src/Model/Api/UserManagement/Company/CompanyWriteRepositoryInterface.php": 1155323634, "src/Model/Api/UserManagement/CompanyCategory/LDAP/CompanyCategoryReadRepositoryLDAP.php": 1629351554, "src/Model/Api/UserManagement/CompanyCategory/LDAP/CompanyCategoryWriteRepositoryLDAP.php": 3416295239, "src/Model/Api/UserManagement/CompanyCategory/CompanyCategoryWriteRepositoryInterface.php": *********, "src/Model/Api/UserManagement/CompanyCategory/CompanyCategory.php": *********, "src/Model/Api/UserManagement/CompanyCategory/Redis/CompanyCategoryWriteRepositoryRedis.php": 3977837887, "src/Model/Api/UserManagement/CompanyCategory/Redis/CompanyCategoryReadRepositoryRedis.php": 3350635982, "src/Model/Api/UserManagement/CompanyCategory/CompanyCategoryReadRepositoryInterface.php": 3112179125, "src/Model/JobApplication/ApplicationLogging.php": 2566965605, "src/Model/JobApplication/JobApplication.php": 3721868362, "src/Model/JobApplication/JobApplicationRepository.php": 1822987519, "src/Monolog/ABUSProcessor.php": 4036078725, "src/Exception/SettingNotFoundException.php": 2015016718, "src/Exception/APIS/UnknownSectionException.php": 3121419392, "src/Exception/APIS/ReferenceNotFoundException.php": 3949294279, "src/Exception/APIS/InformationAlreadyExists.php": 1216424737, "src/Exception/APIS/InformationNotFoundException.php": 3867779473, "src/Exception/APIS/ConfigurationGetterException.php": 2797194402, "src/Exception/APIS/FavoriteNotFoundException.php": 1437334924, "src/Exception/APIS/OptinNotFoundException.php": 1021873000, "src/Exception/APIS/UnsupportedDataViewException.php": 1132764488, "src/Exception/APIS/FileNotFoundException.php": 3545021720, "src/Exception/PDFParseException.php": 3473698775, "src/Exception/FileHandlerExtensionNotAllowedException.php": 1561626142, "src/Exception/Box/Nextcloud/NextcloudException.php": *********, "src/Exception/Box/Nextcloud/NotAuthenticatedException.php": 2335158604, "src/Exception/Box/Nextcloud/DoNotListException.php": 4278815347, "src/Exception/ADCompanyCategoryNotFoundException.php": 3770370939, "src/Exception/ADPasswordPolicyException.php": 2232461917, "src/Exception/ADUserNotFoundException.php": 1107711663, "src/Exception/ConfigEmptyException.php": *********, "src/Exception/ConfigNotFoundException.php": 1405616261, "src/Exception/FileHandlerPermissionNotSetException.php": 1258804083, "src/Exception/FileHandlerFileSizeExceededException.php": 2181631867, "src/Exception/ParentConfigNotFoundException.php": 1847230395, "src/Exception/ModuleNotFoundException.php": 2320655098, "src/Exception/News/NewsNotFoundException.php": 3809468072, "src/Exception/BrokenLinkException.php": 47766082, "src/Exception/WerksvertretungNotFoundException.php": 3860625119, "src/Exception/MailerException.php": *********, "src/Exception/FolderAlreadyExistsException.php": 3266407850, "src/Exception/InvalidTaskPostDataException.php": 4267823148, "src/Exception/ADCompanyNotFoundException.php": 1081097572, "src/Exception/TaskRunnerException.php": 2227907945, "src/Exception/JobApplicationNotFoundException.php": 1070184091, "src/Exception/FileInDatabaseNotFoundException.php": 3634643531, "src/Exception/FileHandlerValuesMissingException.php": 4243878468, "src/Exception/GitlabException.php": *********, "src/Exception/ImageUploadException.php": 1194995492, "src/Exception/ADGroupNotAllowedException.php": 3083657239, "src/Exception/ADGroupNotFoundException.php": 1292739526, "src/Exception/FileHandlerUploadedFileInvalidException.php": *********, "src/Exception/LanguageNotSupportedException.php": 4086736032, "src/Exception/API/MimetypeUnsupportedException.php": *********, "src/Exception/API/UserManagement/UserCreateException.php": 1992450539, "src/Exception/API/UserManagement/CompanyCreateException.php": 3588739200, "src/Exception/ADPermissionDeniedException.php": 3122680200, "src/Exception/PermissionNotHandledException.php": 3963695933, "src/Exception/MultipleADUsersFoundException.php": *********, "src/Exception/FileHandlerMimeTypeNotAllowedException.php": 1198098802, "src/Twig/TwigExtension.php": 2353028083, "src/Controller/FormsReporting/FormsReportingController.php": 3377063395, "src/Controller/APIS/Frontend/FrontendAJAXController.php": 1154287805, "src/Controller/APIS/Frontend/FrontendController.php": 1924568999, "src/Controller/APIS/Backend/BackendController.php": 1287537274, "src/Controller/APIS/Backend/BackendAJAXController.php": 2981979486, "src/Controller/Assets/AssetsController.php": 1730527858, "src/Controller/Box/NextcloudController.php": 1078024984, "src/Controller/Box/BoxController.php": *********, "src/Controller/Box/BoxAPIController.php": *********, "src/Controller/Service/GoogleMapsDistanceMatrixController.php": *********, "src/Controller/Service/ServiceController.php": 3973138595, "src/Controller/ABUSVital/ABUSVital.php": 1613059728, "src/Controller/VideoConference/VideoConferenceController.php": 1788202037, "src/Controller/#UserManagement/PermissionPackageController.php": 2253775590, "src/Controller/#UserManagement/UserController.php": 4136749111, "src/Controller/#UserManagement/CompanyController.php": 1340998102, "src/Controller/#UserManagement/AjaxController.php": 3019541985, "src/Controller/TechnicalData/TechnicalDataController.php": *********, "src/Controller/ABUTools/ResponseController.php": 3730974501, "src/Controller/TaskRunner/TaskRunnerController.php": 1175901326, "src/Controller/Reporting/ReportingController.php": 2755714023, "src/Controller/Alert/AlertController.php": *********, "src/Controller/FamilyDay/FamilyDay.php": 2378430510, "src/Controller/Frame/FrameController.php": 1687754508, "src/Controller/Frame/NewsController.php": 1944633236, "src/Controller/Frame/ModuleRequestController.php": 4246049197, "src/Controller/Frame/ReportProblemController.php": 1185107785, "src/Controller/Frame/iFrameController.php": 3157795446, "src/Controller/Frame/NewsAJAXController.php": *********, "src/Controller/Frame/FrameAJAXController.php": 2771917836, "src/Controller/Management/WebsiteController.php": 1915784728, "src/Controller/Management/UserController.php": 1617084943, "src/Controller/Management/ReportingController.php": 3374337767, "src/Controller/Management/ProblemController.php": 2220864230, "src/Controller/Management/UserAJAXController.php": *********, "src/Controller/Api/SecurityController.php": 2766638510, "src/Controller/Api/ABUKonfisController.php": *********, "src/Controller/Api/UserController.php": 8479654, "src/Controller/Api/CompanyController.php": *********, "src/Controller/JobApplication/JobApplicationController.php": 2062934359, "src/Controller/UserManagement/PermissionPackageController.php": 1825206497, "src/Controller/UserManagement/AjaxWriteController.php": 1195513579, "src/Controller/UserManagement/UserController.php": *********, "src/Controller/UserManagement/CompanyController.php": 2223722324, "src/Controller/UserManagement/AjaxController.php": 3744150231, "src/Controller/UserManagement/AjaxReadController.php": *********, "src/Controller/Documentation/Documentation.php": 2137485949, "src/Entity/FormsReporting/Name.php": *********, "src/Entity/APIS/Favorite.php": 2447693526, "src/Entity/APIS/MarkedAsRead.php": 1200229863, "src/Entity/APIS/Information.php": 2479215610, "src/Entity/APIS/File.php": 3319451925, "src/Entity/APIS/Optin.php": 2051222223, "src/Entity/APIS/Viewed.php": 1969235288, "src/Entity/APIS/Reference.php": 1718922262, "src/Entity/Box/Searchtags.php": 1227546531, "src/Entity/Box/Logsearch.php": 2988767226, "src/Entity/Box/Logsecurity.php": 2401787107, "src/Entity/Box/FilesystemIndex.php": *********, "src/Entity/Box/Logdownload.php": 3857626898, "src/Entity/VideoConference/VideoConference.php": 1985205351, "src/Entity/TaskRunner/Task.php": 3302843686, "src/Entity/News/MarkedAsRead.php": 2047775758, "src/Entity/News/File.php": 1288284510, "src/Entity/News/News.php": 1051592008, "src/Entity/Api/PermissionPackage.php": 2958863327, "src/Entity/Api/ForgotPassword.php": 4203592684, "src/Entity/Api/CompanyResponsibility.php": 3352622550, "src/Entity/Api/Company.php": 1156396322, "src/Kernel.php": 3080875123, "src/EventListener/ExceptionListener.php": 2473271399, "src/EventListener/AuthenticationListener.php": 4035065692, "src/EventListener/LocaleListener.php": 3627393110, "src/EventListener/SwitchUserListener.php": *********, "src/EventListener/LoggerListener.php": 2537950974, "src/EventListener/LogoutListener.php": 1416562555, "src/EventListener/KernelListener.php": 1091191667, "src/Command/APIS/PublishCommand.php": 1792561698, "src/Command/APIS/AddCategoryCommand.php": 4095815270, "src/Command/APIS/RemoveCategoryCommand.php": 1906826298, "src/Command/Box/CrawlerCommand.php": *********, "src/Command/Box/NextcloudTagsCommand.php": 2784153398, "src/Command/Box/NextcloudConfigParserCommand.php": 3572215927, "src/Command/Box/NextcloudElasticsearchCommand.php": 2263029575, "src/Command/Box/NextcloudTempCleanerCommand.php": 3383921777, "src/Command/Service/GoogleMapsDistanceMatrixCommand.php": 3233942649, "src/Command/TechnicalData/DailyMailCommand.php": 785886262, "src/Command/TechnicalData/DailyMailViaABUKonfisCommand.php": 3982429078, "src/Command/TaskRunner/TaskRunnerCommand.php": 885000622, "src/Command/CADExchange/CrawlerCommand.php": 4168327131, "src/Command/Api/LDAP2Redis.php": 3646025845, "src/Command/Api/LDAP2RedisCommand.php": 3610773480, "src/Repository/FormsReporting/FormsReportingRepository.php": 1648097023, "src/Repository/APIS/ViewedRepository.php": 4230627778, "src/Repository/APIS/MarkedAsReadRepository.php": *********, "src/Repository/APIS/FavoriteRepository.php": 1468204021, "src/Repository/APIS/InformationRepository.php": *********, "src/Repository/Box/FilesystemIndexRepository.php": 1789704760, "src/Repository/VideoConference/VideoConferenceRepository.php": **********, "src/Repository/News/NewsRepository.php": **********, "src/Repository/Information/FavoriteRepository.php": **********, "src/Repository/Information/InformationRepository.php": **********, "src/Repository/Api/CompanyResponsibilityRepository.php": **********, "src/Repository/Api/PermissionPackageRepository.php": **********, "src/Repository/Api/CompanyRepository.php": **********, "src/Security/User/ABUSUser.php": **********, "src/Security/User/ABUSUserProvider.php": **********, "src/Security/FormLoginAuthenticator.php": **********, "src/Event/LoggerEvent.php": **********}}