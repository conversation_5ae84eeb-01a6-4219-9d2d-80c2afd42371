@import '../vendor/dynamicTheme/css/bootstrapCustomScss/custom-variables';

body {

  font-family: Helvetica,Arial,sans-serif !important;

  background-color: #f2f2f2 !important;

  [ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak], .ng-cloak, .x-ng-cloak {
    display: none !important;
  }

  .bgABUSBlue {

    & > .panel-heading {
      background-color: #0058a1 !important;
      border-color: #00467b !important;
      color: #ffffff;
    }
  }

  .bgABUSYellow {

    & > .panel-heading {
      background-color: #ffc500 !important;
      border-color: #ffaa00 !important;
      color: #000000;
    }
  }

  .bgDarkGray {

    & > .panel-heading {
      background-color: #8a8a8a !important;

      .panel-title {
        color: #ffffff !important;
      }

      i {
        color: #ffffff;
        font-size: 24px !important;
        position: absolute;
        top: 15px;
        right: 20px;

      }
    }

  }

  .nomargin, .noMargin {
    margin: 0;
  }

  .nopadding, .noPadding {
    padding: 0;
  }

  .pl30 {
    padding-left: 30px;
  }

  .mt30 {
    margin-top: 30px;
  }

  .btn-primary, .btn-primary:hover {
    border-color: transparent;
  }

  .clickable {
    cursor: pointer;
  }

  .input-group .form-control {
    z-index: 1;
  }

  div.page-content,
  div.page-content-wrapper {
    min-height: calc(100vh - 50px);

    &.iframe, &.iframeWithBorder {

      div.page-content-inner {

        iframe {
          //display:block;
          width: 100%;
          height: calc(100vh - 50px);
          border: none;

          &.mobile-iframe {
            //transform: scale(0.5);
            //aufgrund Scaling Größe verdoppeln
            //width: 200%;
            //height: 200%;
            //height: calc(100vh / 2 - 50px);
            //transform-origin:  0 0;
          }
        }
      }
    }

    &.iframe {
      div.page-content-inner {
        padding: 0;
      }
    }
  }

  .scroll-wrapper {
    -webkit-overflow-scrolling: touch;
    overflow-y: scroll;
  }


  div#page-header {
    margin-bottom: 0 !important;
  }

  div#header {
    background-color: #0058a1;

    .navbar-brand {
      background-color: #0058a1;

      img.logo {
        display: block !important;
      }

      img.logo-small {
        display: none;
      }

      &.collapse-sidebar {
        img.logo {
          display: none !important;
        }

        img.logo-small {
          display: block !important;
          padding-left: 5px;
        }
      }
    }

    .nav.navbar-nav > li > a {
      color: #ffffff;

      &:focus {
        background-color: #0058a1;
      }

      &:hover, &:active {
        color: #333333;
        background-color: #ffc500;
      }
    }

    .navbar-nav > li.open > a {
      background-color: #0058a1;
    }
  }

  div#page-header {
    background-color: #e8e8e8 !important;
  }

  div#header.userMode {
    background-color: #ff0084;

    span.username {
      font-size: 24px;
      color: #FFFFFF;
      line-height: 50px;
      float: right;
      padding-right: 30px;
    }

    .navbar-brand {
      background-color: inherit !important;
    }
  }

  .page-navbar {

    .navbar-brand {
      background-color: #fff;

      &.collapse-sidebar {
        padding-left: 0;
      }
    }
  }

  .page-sidebar {

    .side-nav {

      font-size: 0.8125rem;

      .nav > li ul.sub > li a:after {
        height: 100%;
      }
    }

    .user-info {
      font-size: 0.8em;
      color: #ffffff;
      width: 100%;

      .avatar {
        float: right;

        img {
          margin-right: 0;
        }
      }
    }

    .language {

      text-align: center;

      img {
        margin: 10px;
        opacity: 0.6;
        height: 13px;
        width: 20px;
      }

      img.active,
      a:hover img {
        opacity: 1;
      }

    }

    .collapse-sidebar {
      a#requestService {
        display: none;
      }
    }
  }

  .right-sidebar {

    .sidebar-inner {
      //background-color: #303946;
      color: #ffffff;
      font-size: 0.9em;

      .sidebar-panel .sidebar-panel-title {
        padding-left: 10px;
      }

      div.userModeTypeahead .dropdown-menu {
        margin-left: 0 !important;
        right: 10px !important;
        left: auto !important;
      }

      //input {
      //  width: 100%;
      //  color: #000000;
      //  height: 34px;
      //  padding: 6px 12px;
      //}

      div.content {
        clear: both;
        padding: 10px;
      }

      hr {
        margin-left: -10px !important;
        margin-right: -10px !important;
        border-top: 1px solid $abus-grey-5;
      }

    }
  }

  div#search {
    margin-right: -20px;
    margin-left: -20px;
    margin-bottom: 20px;
    background-color: #FFAA00;

    div.searchBox {
      padding-top: 15px;
    }

    .btn-group {
      //padding-left: 0;
      button {
        width: 100%;
        text-align: left;
      }

      .dropdown-menu {
        padding-right: 10px;
      }

    }

  }

  a#requestService {
    background-color: #3F5B82;

  }

  div.panel {

    &.noBottomMargin {
      margin-bottom: 0 !important;
    }

    .panel-title {
      div.icon {
        float: left;
        margin-right: 10px;
      }
    }
  }

  .tile-heading {
    background: #ffc500 !important;
    border-color: #999999 !important;
    text-align: center;

    h4 {
      color: #333333;
      font-weight: bold !important;
    }
  }

  .tile {
    height: 130px;
    padding: auto 0;
    vertical-align: middle;
    text-align: center;
    background: rgb(223,223,223); /* Old browsers */
    background: -moz-linear-gradient(top, rgba(223,223,223,1) 0%, rgba(122,122,122,1) 88%, rgba(122,122,122,1) 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(223,223,223,1)), color-stop(88%,rgba(122,122,122,1)), color-stop(100%,rgba(122,122,122,1))); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, rgba(223,223,223,1) 0%,rgba(122,122,122,1) 88%,rgba(122,122,122,1) 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, rgba(223,223,223,1) 0%,rgba(122,122,122,1) 88%,rgba(122,122,122,1) 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, rgba(223,223,223,1) 0%,rgba(122,122,122,1) 88%,rgba(122,122,122,1) 100%); /* IE10+ */
    background: linear-gradient(to bottom, rgba(223,223,223,1) 0%,rgba(122,122,122,1) 88%,rgba(122,122,122,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#dfdfdf', endColorstr='#7a7a7a',GradientType=0 ); /* IE6-9 */
  }

  div#backButton {
    margin-top: 10px;
  }

  ul.todo {
    background-color: #CC9393;
    margin-top: 10px;
    border: #c0beb5 1px solid;
    padding-top: 10px;
    padding-bottom: 10px;
  }
}

@media (max-width: 991px) {
  .page-content #page-header .page-header {
    text-align: left;
  }
}

@media (min-width: 992px) {
  div.page-content,
  div.page-content-wrapper {
    &.iframe {
      div.page-content-inner {
        width: calc(100vw - #{$sidebar-width} - 15 - #{$right-sidebar-width});

        &.rsidebar-collapsed {
          width: calc(100vw - #{$sidebar-width} - 15);

          &.lsidebar-collapsed {
            width: calc(100vw - #{$sidebar-collapse-width});
          }
        }

        &.lsidebar-collapsed {
          width: calc(100vw - #{$sidebar-collapse-width} - #{$right-sidebar-width});
        }
      }
    }
  }
}

div.pwComplexityInfo {
  display: block;
  position: relative;
  background-color: rgba(50, 50, 50, 0.95);
  max-width: 300px;
  min-height: 100px;
  padding: 8px;
  color: #B1B1B0;
  z-index: 100;

  border-radius: 5px;
  border: #808080 2px solid;

  [x-arrow] {
    border: 8px solid transparent;
    position: absolute;
  }

  &[x-placement="right"] {
    margin-left: 10px;

    [x-arrow] {
      left: -8px;
      border-left-width: 0px;
      border-right-color: #808080;
    }
  }

  li{

    &.fullfilled {
      color: greenyellow;
    }
  }
}

// Popper
.active-users-popper .v-popper__inner {
  box-shadow: 8px 5px 30px rgba(0,0,0,0.5) !important;
}

// TODO: Remove after update to Bootstrap 5
.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 1rem;
}
