<template>
  <div class="activeUsers">
    <div class="sidebar-panel clickable" @click="minimize()">
      <h5 class="sidebar-panel-title">
        {{ $t('frame/active_users') }}
        <div style="float: right">
          <div v-show="!minimizedInternal">
            <i class="fas fa-minus-square"></i>
          </div>
          <div v-show="minimizedInternal">
            <i class="fas fa-plus-square"></i>
          </div>
        </div>
      </h5>
    </div>

    <div v-show="!minimizedInternal" class="content">
      <div class="online-summary">{{ onlineUsers.length }} Online</div>
      <pagination :elements-per-page="20" :items="onlineUsers">
        <template #default="{ pagedData }">
          <div class="usersOnline">
            <div v-for="(user, index) in pagedData" :key="index" class="user">
              <VMenu
                placement="right"
                :distance="20"
                :prevent-overflow="true"
                :flip="true"
                boundary="body"
                popper-class="active-users-popper"
                strategy="fixed">
                <div class="name" role="button" aria-haspopup="dialog" tabindex="0">
                  {{ user.firstname }} {{ user.lastname }}
                </div>

                <template #popper>
                  <div class="popper">
                    <div class="userInfo">
                      <section class="section section--header">
                        <div class="d-flex align-items-start gap-3 flex-wrap">
                          <div class="user-fields flex-grow-1 d-flex flex-column gap-2">
                            <div class="field d-flex align-items-start">
                              <div class="label fw-bold me-2">{{ $t('frame/name') }}:</div>
                              <div class="value">{{ user.firstname }} {{ user.lastname }}</div>
                            </div>

                            <div v-if="user.company" class="field d-flex align-items-start">
                              <div class="label fw-bold me-2">{{ $t('frame/company') }}:</div>
                              <div class="value">{{ user.company }}</div>
                            </div>

                            <div v-if="user.email" class="field d-flex align-items-start">
                              <div class="label fw-bold me-2">{{ $t('frame/email') }}:</div>
                              <div class="value">
                                <a :href="'mailto:' + user.email">{{ user.email }}</a>
                              </div>
                            </div>

                            <div v-if="user.telephone" class="field d-flex align-items-start">
                              <div class="label fw-bold me-2">{{ $t('frame/telephone') }}:</div>
                              <div class="value">{{ user.telephone }}</div>
                            </div>
                          </div>

                          <div v-if="usermode" class="actions ms-auto">
                            <a class="btn btn-abus-yellow-dark btn-sm" :href="'/?_username=' + user.email">
                              <i class="fas fa-user-secret"></i>&nbsp;{{ $t('frame/impersonate') }}
                            </a>
                          </div>
                        </div>

                        <hr class="section-sep" />
                      </section>

                      <section class="section section--groups">
                        <div class="badge badge-success mb-2">{{ $t('frame/right_sidebar_groups') }}:</div>

                        <div class="groups-list">
                          <div
                            v-for="group in user.groups.slice(0).sort()"
                            :key="group"
                            class="group"
                            v-html="group.replace(/^ROLE_/g, '')" />
                        </div>
                      </section>
                    </div>
                  </div>
                </template>
              </VMenu>
            </div>
          </div>
        </template>
      </pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, defineProps } from 'vue';
import axios from 'axios';
import Pagination from '../helper/Pagination.vue';

interface User {
  firstname: string;
  lastname: string;
  company?: string;
  email?: string;
  telephone?: string;
  groups: string[];
}

defineProps<{
  minimized?: boolean;
  usermode?: boolean;
}>();

let refresher: number | undefined;
const onlineUsers = ref<User[]>([]);
const minimizedInternal = ref(false);

function usersOnline() {
  axios.get<User[]>('/usersOnline').then((res) => {
    onlineUsers.value = res.data;
  });
}

function minimize() {
  minimizedInternal.value = !minimizedInternal.value;
  localStorage.setItem('usersOnlineMinimized', minimizedInternal.value ? 'true' : 'false');
}

onMounted(() => {
  minimizedInternal.value = false;

  const stored = localStorage.getItem('usersOnlineMinimized');
  if (stored !== null) {
    minimizedInternal.value = stored === 'true';
  }

  usersOnline();
  refresher = window.setInterval(usersOnline, 30000);
});

onBeforeUnmount(() => {
  if (refresher) window.clearInterval(refresher);
});
</script>

<style lang="scss" scoped>
@import '@assets/public/vendor/dynamicTheme/css/bootstrapCustomScss/custom-variables';

.popper {
  box-shadow: 10px 10px 20px rgba(0, 0, 0, 0.75);
  border: 0;
  font-size: 14px;

  .badge {
    font-size: 12px;
  }
}

.name {
  cursor: help;
}

.userInfo {
  position: relative;
  background-color: #2d3644;
  color: #fff;
  min-width: 600px;
  min-height: 100px;
  padding: 15px;
  z-index: 100;
  border: 2px solid white;

  a {
    color: #fff;

    &.btn {
      color: #fff;
    }
  }

  .section--header {
    .section-sep {
      margin: 10px 5px;
      border-color: rgba(255, 255, 255, 0.15);
    }
  }

  .field {
    .label {
      flex: 0 0 20%;
      min-width: 90px;
      text-align: left;
    }

    .value {
      flex: 1 1 auto;
      text-align: left;
      word-break: break-word;
    }
  }

  .section--groups {
    width: 100%;

    .groups-list {
      max-height: 50vh;
      overflow-y: auto;
      display: grid;
      grid-auto-rows: min-content;
      row-gap: 0.25rem;

      .group {
        text-align: left;
        padding: 0;
      }

      /* scrollbar (WebKit) */
      &::-webkit-scrollbar {
        width: 5px;
        height: 5px;
      }
      &::-webkit-scrollbar-track {
        background: #2d3644;
      }
      &::-webkit-scrollbar-thumb {
        background: #888;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
    }
  }
}
</style>
