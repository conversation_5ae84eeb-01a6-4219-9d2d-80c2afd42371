{"name": "portal", "version": "3.0.0", "description": "ABUS Kransysteme GmbH Portal", "main": "/public/index.php", "directories": {"doc": "doc"}, "repository": {"type": "git", "url": "ssh://******************************:2289/abus/portal2018.git"}, "author": "<PERSON>", "contributors": ["<PERSON><PERSON><PERSON>"], "dependencies": {"@ckeditor/ckeditor5-vue2": "^3.0.1", "@fortawesome/fontawesome": "^1.1.8", "@fortawesome/fontawesome-svg-core": "^1.2.36", "@fortawesome/free-brands-svg-icons": "^5.15.4", "@fortawesome/pro-light-svg-icons": "^5.11.2", "@fortawesome/pro-regular-svg-icons": "^5.11.2", "@fortawesome/pro-solid-svg-icons": "^5.11.2", "@fortawesome/vue-fontawesome": "0.0.23", "@types/js-yaml": "^4.0.5", "axios": "^0.19.2", "base-64": "^0.1.0", "bootstrap": "^4.6.2", "bootstrap-vue": "^2.21.2", "chart.js": "^2.9.2", "ckeditor5": "^45.2.1", "codemirror": "^5.49.2", "css-browser-selector": "^0.6.5", "dayjs": "^1.11.13", "detect-browser": "^5.3.0", "fastclick": "^1.0.6", "file-saver": "^1.3.8", "fine-uploader": "^5.16.2", "floating-vue": "^1.0.0-beta.19", "font-awesome": "^4.7.0", "jquery": "^3.4.1", "jquery-slimscroll": "^1.3.8", "jquery-ui-dist": "^1.13.2", "js-yaml": "^4.1.0", "lightbox2": "^2.11.3", "lodash": "^4.17.21", "moment": "^2.29.4", "pc-bootstrap4-datetimepicker": "^4.17.51", "qs": "^6.14.0", "re-tree": "0.1.7", "svg-country-flags": "^1.2.6", "typeahead.js": "^0.11.1", "vue-bootstrap-datetimepicker": "^5.0.1", "vue-ckeditor2": "^2.1.5", "vue-fineuploader": "^2.0.0-rc.9", "vue-i18n": "^7.8.1", "vue-infinite-scroll": "^2.0.2", "vue-js-toggle-button": "^1.3.3", "vue-simple-spinner": "^1.2.10", "vue-snotify": "3.2.1", "vuex": "^3.6.2", "vuex-class": "^0.3.2"}, "devDependencies": {"@eslint/js": "^9.30.0", "@rollup/plugin-inject": "^5.0.5", "@types/base64-js": "^1.3.0", "@types/ckeditor": "0.0.49", "@types/es6-promise": "0.0.33", "@types/lodash": "^4.14.184", "@types/qs": "^6.14.0", "@types/vue-i18n": "^6.1.3", "@types/vue-scrollto": "^2.17.1", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue2": "^2.3.3", "core-js": "^3.4.8", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-vue": "^10.2.0", "globals": "^16.2.0", "jest": "^26.6.3", "jest-vue-preprocessor": "^1.7.1", "less": "^4.1.3", "prettier": "^3.6.2", "prettier-plugin-organize-attributes": "^1.0.0", "regenerator-runtime": "^0.13.9", "rollup-plugin-copy": "^3.5.0", "sass-embedded": "^1.89.2", "ts-jest": "^26.5.6", "typescript": "^4.8.2", "typescript-eslint": "^8.35.0", "vite": "^6.3.5", "vite-plugin-html": "^3.2.2", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-pwa": "^1.0.0", "vite-plugin-static-copy": "^3.1.0", "vue": "^2.7.16", "vue-class-component": "^6.3.2", "vue-property-decorator": "^6.1.0", "vue-template-compiler": "^2.6.14", "vue-test-utils": "^1.0.0-beta.11"}, "scripts": {"cache:clear": "sudo rm -rfdv ./var/cache/dev/*", "phpcs": "docker exec -it abus_portal2018_php-fpm php phpcs.phar --standard=psr12 --error-severity=1 --warning-severity=8 --extensions=php ./src", "phpcbf": "docker exec -it abus_portal2018_php-fpm php phpcbf.phar --standard=psr12 --error-severity=1 --warning-severity=8 --extensions=php ./src", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --runInBand", "build": "vite build", "build:dev": "NODE_ENV=development vite build --mode development", "watch": "NODE_ENV=development vite build --mode development --watch", "lint": "eslint . --ext .vue,.ts,.js", "lint:fix": "eslint . --ext .vue,.ts,.js --fix", "format": "prettier --check .", "format:fix": "prettier --write ."}, "browserslist": ["> 1%", "ie 9-11"], "jest": {"roots": ["<rootDir>/assets"], "moduleNameMapper": {"^vue$": "vue/dist/vue.common.js"}, "moduleFileExtensions": ["ts", "tsx", "js", "vue"], "transform": {"\\.(ts|tsx)$": "<rootDir>/node_modules/ts-jest/preprocessor.js", "^.+\\.js$": "<rootDir>/node_modules/babel-jest", ".*\\.(vue)$": "<rootDir>/node_modules/jest-vue-preprocessor"}, "testRegex": "/__tests__/.*\\.spec\\.ts$"}}